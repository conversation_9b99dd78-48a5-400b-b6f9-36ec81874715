'use client'

import { useState, useEffect } from 'react'
import '@/styles/admin.css'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AnimatedButton, PrimaryButton, SecondaryButton, DangerButton, OutlineButton, QuickButton } from '@/components/ui/animated-button'
import CompactActionsDropdown from '@/components/ui/compact-actions-dropdown'
import SimpleActions from '@/components/ui/simple-actions'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { getSoftColorClasses } from '@/utils/softColors'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { api, cantieriApi, usersApi } from '@/lib/api'
import { User, Cantiere } from '@/types'
import UserForm from '@/components/admin/UserForm'
import DatabaseView from '@/components/admin/DatabaseView'
import ResetDatabase from '@/components/admin/ResetDatabase'
import TipologieCaviManager from '@/components/admin/TipologieCaviManager'
import {
  Settings,
  Users,
  Building2,
  Search,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Shield,
  Key,
  Loader2,
  UserPlus,
  LogIn,
  Cable,
  Database,
  RotateCcw,
  RefreshCw
} from 'lucide-react'

export default function AdminPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('visualizza-utenti')
  const [searchTerm, setSearchTerm] = useState('')
  const [users, setUsers] = useState<User[]>([])
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' })

  const { user, impersonateUser } = useAuth()

  // Carica dati dal backend
  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {
        const usersData = await usersApi.getUsers()
        setUsers(usersData)
      } else if (activeTab === 'cantieri') {
        const cantieriData = await cantieriApi.getCantieri()
        setCantieri(cantieriData)
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || error.message || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditUser = (userToEdit: User) => {
    setSelectedUser(userToEdit)
    setActiveTab('modifica-utente')
  }

  const handleToggleUserStatus = async (userId: number) => {
    try {
      await usersApi.toggleUserStatus(userId)
      loadData() // Ricarica i dati
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante la modifica dello stato utente')
    }
  }

  const handleDeleteUser = async (userId: number) => {
    if (confirm('Sei sicuro di voler eliminare questo utente?')) {
      try {
        await usersApi.deleteUser(userId)
        loadData() // Ricarica i dati
      } catch (error: any) {
        setError(error.response?.data?.detail || 'Errore durante l\'eliminazione dell\'utente')
      }
    }
  }

  const handleSaveUser = (savedUser: User) => {
    setSelectedUser(null)
    setActiveTab('visualizza-utenti')
    loadData() // Ricarica i dati
  }

  const handleCancelForm = () => {
    setSelectedUser(null)
    setActiveTab('visualizza-utenti')
  }

  const handleQuickImpersonate = async (targetUser: User) => {
    try {

      await impersonateUser(targetUser.id_utente)

      // Reindirizza in base al ruolo dell'utente impersonato
      if (targetUser.ruolo === 'user') {
        router.push('/cantieri')
      } else if (targetUser.ruolo === 'cantieri_user') {
        router.push('/cavi')
      } else {
        router.push('/')
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || error.message || 'Errore durante l\'impersonificazione')
    }
  }

  // Helper functions per i badge

  const getRuoloBadge = (ruolo: string) => {
    let colorType: keyof typeof getSoftColorClasses = 'NEUTRAL'

    switch (ruolo) {
      case 'owner':
        colorType = 'PROGRESS'
        break
      case 'user':
        colorType = 'INFO'
        break
      case 'cantieri_user':
        colorType = 'SUCCESS'
        break
      default:
        colorType = 'NEUTRAL'
        break
    }

    const colorClasses = getSoftColorClasses(colorType)
    return <Badge className={colorClasses.badge}>{ruolo}</Badge>
  }

  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {
    let colorType: keyof typeof getSoftColorClasses = 'SUCCESS'
    let label = 'Attivo'
    let icon = '●'

    if (!abilitato) {
      colorType = 'ERROR'
      label = 'Disabilitato'
      icon = '●'
    } else if (data_scadenza) {
      const scadenza = new Date(data_scadenza)
      const oggi = new Date()

      if (scadenza < oggi) {
        colorType = 'ERROR'
        label = 'Scaduto'
        icon = '⚠'
      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {
        colorType = 'WARNING'
        label = 'In Scadenza'
        icon = '⏰'
      } else {
        icon = '✓'
      }
    } else {
      icon = '✓'
    }

    const colorClasses = getSoftColorClasses(colorType)
    return (
      <Badge className={`${colorClasses.badge} flex items-center gap-1`}>
        <span className="text-xs" role="img" aria-hidden="true">{icon}</span>
        <span>{label}</span>
      </Badge>
    )
  }

  const filteredUsers = users.filter(u =>
    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Verifica se l'utente ha permessi di amministrazione
  // Durante il logout, user diventa null, quindi reindirizza direttamente al login
  if (!user) {
    if (typeof window !== 'undefined') {
      window.location.replace('/login')
    }
    return null
  }

  if (user.ruolo !== 'owner') {
    if (typeof window !== 'undefined') {
      window.location.replace('/login')
    }
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">

        {/* Header con titolo e descrizione */}
        <div className="text-center space-y-2 mb-6">
          <h1 className="text-3xl font-bold text-slate-900">Pannello di Amministrazione</h1>
          <p className="text-slate-600">Gestisci utenti, database e configurazioni del sistema CABLYS</p>
        </div>

        {/* Tabs - Design migliorato con separazione visiva per azioni pericolose */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="space-y-4">
            {/* Tab principali */}
            <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-1">
              <div className={`grid w-full ${selectedUser ? 'grid-cols-5' : 'grid-cols-4'} gap-1`}>
                <TabsTrigger
                  value="visualizza-utenti"
                  className="admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent"
                >
                  <Users className="h-4 w-4" />
                  <span className="font-medium">Gestione Utenti</span>
                </TabsTrigger>
                <TabsTrigger
                  value="crea-utente"
                  className="admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent"
                >
                  <UserPlus className="h-4 w-4" />
                  <span className="font-medium">Crea Nuovo Utente</span>
                </TabsTrigger>
                {selectedUser && (
                  <TabsTrigger
                    value="modifica-utente"
                    className="admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent"
                  >
                    <Edit className="h-4 w-4" />
                    <span className="font-medium">Modifica Utente</span>
                  </TabsTrigger>
                )}
                <TabsTrigger
                  value="database-tipologie-cavi"
                  className="admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent"
                >
                  <Cable className="h-4 w-4" />
                  <span className="font-medium">Database Tipologie Cavi</span>
                </TabsTrigger>
                <TabsTrigger
                  value="visualizza-database-raw"
                  className="admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent"
                >
                  <Database className="h-4 w-4" />
                  <span className="font-medium">Gestione Dati Avanzata</span>
                </TabsTrigger>
              </div>
            </div>

            {/* Sezione separata per azioni pericolose */}
            <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-200 shadow-sm p-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 px-4 py-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-red-800">Impostazioni Avanzate e Pericolose</span>
                </div>
                <TabsTrigger
                  value="reset-database"
                  className="admin-tab-danger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-red-100 data-[state=active]:text-red-700 data-[state=active]:border-red-300 data-[state=active]:shadow-sm hover:bg-red-100 hover:text-red-700 border border-transparent text-red-600 font-medium"
                >
                  <RotateCcw className="h-4 w-4" />
                  <span>Reset Database</span>
                </TabsTrigger>
              </div>
            </div>
          </div>

          {/* Tab Visualizza Utenti */}
          <TabsContent value="visualizza-utenti" className="space-y-4">

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600">{error}</p>
              </div>
            )}

            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      Lista Utenti
                    </CardTitle>
                    <CardDescription>
                      Gestisci tutti gli utenti del sistema CABLYS
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                      <Input
                        placeholder="Cerca per username, email o ragione sociale..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-80"
                      />
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {filteredUsers.length} utenti
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <div className="rounded-md border">
                    <Table className="min-w-full">
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[60px] text-center">ID</TableHead>
                        <TableHead className="w-[120px]">Username</TableHead>
                        <TableHead className="w-[100px] text-center">Password</TableHead>
                        <TableHead className="w-[100px] text-center">Ruolo</TableHead>
                        <TableHead className="w-[250px]">Ragione Sociale</TableHead>
                        <TableHead className="w-[200px]">Email</TableHead>
                        <TableHead className="w-[120px] text-center">VAT</TableHead>
                        <TableHead className="w-[100px] text-center">Nazione</TableHead>
                        <TableHead className="w-[150px]">Referente</TableHead>
                        <TableHead className="w-[100px] text-center">Scadenza</TableHead>
                        <TableHead className="w-[100px] text-center">Stato</TableHead>
                        <TableHead className="w-[120px] text-center">Azioni</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8">
                            <div className="flex items-center justify-center gap-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Caricamento...
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : users.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={12} className="text-center py-8 text-slate-500">
                            Nessun utente trovato
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredUsers.map((utente) => (
                          <TableRow
                            key={utente.id_utente}
                            className="users-table-row hover:bg-slate-50 transition-all duration-200 hover:shadow-sm"
                          >
                            {/* ID */}
                            <TableCell className="text-center">
                              <Badge variant="outline" className="text-xs font-mono">
                                #{utente.id_utente}
                              </Badge>
                            </TableCell>

                            {/* Username */}
                            <TableCell className="font-semibold text-slate-900">
                              {utente.username}
                            </TableCell>

                            {/* Password - Sempre mascherata per sicurezza */}
                            <TableCell className="text-center">
                              <div className="flex items-center justify-center gap-1">
                                <div className="flex gap-1">
                                  {[...Array(8)].map((_, i) => (
                                    <div key={i} className="w-1.5 h-1.5 bg-slate-400 rounded-full"></div>
                                  ))}
                                </div>
                                <div className={`w-2 h-2 rounded-full ml-2 ${
                                  utente.password_plain ? 'bg-green-500' : 'bg-red-500'
                                }`} title={utente.password_plain ? 'Password configurata' : 'Password non configurata'}></div>
                              </div>
                            </TableCell>

                            {/* Ruolo */}
                            <TableCell className="text-center">
                              {getRuoloBadge(utente.ruolo)}
                            </TableCell>

                            {/* Ragione Sociale */}
                            <TableCell className="max-w-[250px] truncate" title={utente.ragione_sociale}>
                              <span className="text-slate-900">{utente.ragione_sociale || '-'}</span>
                            </TableCell>

                            {/* Email */}
                            <TableCell className="max-w-[200px] truncate text-sm text-slate-600" title={utente.email}>
                              {utente.email || '-'}
                            </TableCell>

                            {/* VAT */}
                            <TableCell className="text-center text-sm text-slate-600">
                              {utente.vat || '-'}
                            </TableCell>

                            {/* Nazione */}
                            <TableCell className="text-center text-sm text-slate-600">
                              {utente.nazione || '-'}
                            </TableCell>

                            {/* Referente */}
                            <TableCell className="max-w-[150px] truncate text-sm text-slate-600" title={utente.referente_aziendale}>
                              {utente.referente_aziendale || '-'}
                            </TableCell>

                            {/* Scadenza */}
                            <TableCell className="text-center text-sm text-slate-600">
                              {utente.data_scadenza ?
                                new Date(utente.data_scadenza).toLocaleDateString('it-IT') :
                                'N/A'
                              }
                            </TableCell>

                            {/* Stato */}
                            <TableCell className="text-center">
                              {getStatusBadge(utente.abilitato, utente.data_scadenza)}
                            </TableCell>

                            {/* Azioni */}
                            <TableCell className="text-center">
                              <div className="flex items-center justify-center gap-2">
                                {/* Icone azioni semplici */}
                                <SimpleActions
                                  user={utente}
                                  onEdit={() => handleEditUser(utente)}
                                  onToggleStatus={() => handleToggleUserStatus(utente.id_utente)}
                                  onDelete={() => handleDeleteUser(utente.id_utente)}
                                />

                                {/* Pulsante Entra */}
                                <PrimaryButton
                                  size="sm"
                                  onClick={() => handleQuickImpersonate(utente)}
                                  disabled={utente.ruolo === 'owner' || !utente.abilitato}
                                  className="px-3 py-1.5 text-xs"
                                  icon={<LogIn className="h-3.5 w-3.5" />}
                                >
                                  Entra
                                </PrimaryButton>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                    </Table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tab Crea Nuovo Utente */}
          <TabsContent value="crea-utente" className="space-y-4">
            <UserForm
              user={null}
              onSave={handleSaveUser}
              onCancel={handleCancelForm}
            />
          </TabsContent>

          {/* Tab Modifica Utente - Visibile solo quando un utente è selezionato */}
          {selectedUser && (
            <TabsContent value="modifica-utente" className="space-y-4">
              <UserForm
                user={selectedUser}
                onSave={handleSaveUser}
                onCancel={handleCancelForm}
              />
            </TabsContent>
          )}

          {/* Tab Database Tipologie Cavi */}
          <TabsContent value="database-tipologie-cavi" className="space-y-4">
            <TipologieCaviManager />
          </TabsContent>

          {/* Tab Visualizza Database Raw */}
          <TabsContent value="visualizza-database-raw" className="space-y-4">
            <DatabaseView />
          </TabsContent>

          {/* Tab Reset Database */}
          <TabsContent value="reset-database" className="space-y-4">
            <ResetDatabase />
          </TabsContent>

        </Tabs>
      </div>
    </div>
  )
}
