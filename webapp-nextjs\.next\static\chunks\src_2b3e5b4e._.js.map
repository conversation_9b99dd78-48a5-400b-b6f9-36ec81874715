{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        data-slot=\"input\"\n        className={cn(\n          \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n          \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cantieri/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cantieriApi } from '@/lib/api'\nimport { Cantiere } from '@/types'\nimport {\n  Building2,\n  Plus,\n  Settings,\n  Trash2,\n  Eye,\n  EyeOff,\n  Search,\n  Copy,\n  Calendar,\n  MapPin,\n  User,\n  Loader2,\n  AlertCircle,\n  Lock,\n  Mail,\n  Shield,\n  CheckCircle,\n  BarChart3,\n  Edit,\n  ArrowRight\n} from 'lucide-react'\n\nexport default function CantieriPage() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n  const [cantieri, setCantieri] = useState<Cantiere[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [cantieriStats, setCantieriStats] = useState<Record<number, { percentuale_avanzamento: number }>>({})\n  const [statsLoading, setStatsLoading] = useState(false)\n\n\n  const [searchTerm, setSearchTerm] = useState('')\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [showPasswordDialog, setShowPasswordDialog] = useState(false)\n  const [showViewPasswordDialog, setShowViewPasswordDialog] = useState(false)\n  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null)\n  const [formData, setFormData] = useState({\n    commessa: '',\n    descrizione: '',\n    nome_cliente: '',\n    indirizzo_cantiere: '',\n    citta_cantiere: '',\n    nazione_cantiere: '',\n    password_cantiere: '',\n    codice_univoco: ''\n  })\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  })\n  const [passwordMode, setPasswordMode] = useState<'change'>('change')\n  const [revealedPassword, setRevealedPassword] = useState('')\n  const [showRevealedPassword, setShowRevealedPassword] = useState(false)\n  const [viewPasswordLoading, setViewPasswordLoading] = useState(false)\n  const [passwordVisibility, setPasswordVisibility] = useState<Record<number, boolean>>({})\n  const [revealedPasswords, setRevealedPasswords] = useState<Record<number, string>>({})\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadCantieri()\n    }\n  }, [isAuthenticated])\n\n  const loadCantieri = async () => {\n    try {\n      setLoading(true)\n      const data = await cantieriApi.getCantieri()\n      setCantieri(data)\n\n      // Carica le statistiche per ogni cantiere\n      await loadCantieriStatistics(data)\n    } catch (error) {\n      setError('Errore nel caricamento dei cantieri')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadCantieriStatistics = async (cantieriData: Cantiere[]) => {\n    try {\n      setStatsLoading(true)\n      const statsPromises = cantieriData.map(async (cantiere) => {\n        try {\n          const stats = await cantieriApi.getCantiereStatistics(cantiere.id_cantiere)\n          return { id: cantiere.id_cantiere, stats }\n        } catch (error) {\n          console.error(`Errore nel caricamento statistiche cantiere ${cantiere.id_cantiere}:`, error)\n          return { id: cantiere.id_cantiere, stats: { percentuale_avanzamento: 0 } }\n        }\n      })\n\n      const results = await Promise.all(statsPromises)\n      const statsMap = results.reduce((acc, { id, stats }) => {\n        acc[id] = stats\n        return acc\n      }, {} as Record<number, { percentuale_avanzamento: number }>)\n\n      setCantieriStats(statsMap)\n    } catch (error) {\n      console.error('Errore nel caricamento delle statistiche:', error)\n    } finally {\n      setStatsLoading(false)\n    }\n  }\n\n  const handleCreateCantiere = async () => {\n    try {\n      await cantieriApi.createCantiere(formData)\n      setShowCreateDialog(false)\n      setFormData({\n        commessa: '',\n        descrizione: '',\n        nome_cliente: '',\n        indirizzo_cantiere: '',\n        citta_cantiere: '',\n        nazione_cantiere: '',\n        password_cantiere: '',\n        codice_univoco: ''\n      })\n      loadCantieri()\n    } catch (error) {\n      setError('Errore nella creazione del cantiere')\n    }\n  }\n\n  const handleEditCantiere = async () => {\n    if (!selectedCantiere) return\n    \n    try {\n      await cantieriApi.updateCantiere(selectedCantiere.id_cantiere, formData)\n      setShowEditDialog(false)\n      setSelectedCantiere(null)\n      loadCantieri()\n    } catch (error) {\n      setError('Errore nella modifica del cantiere')\n    }\n  }\n\n  const handleSelectCantiere = (cantiere: Cantiere) => {\n    // Salva il cantiere selezionato nel localStorage\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())\n    localStorage.setItem('selectedCantiereName', cantiere.commessa)\n    \n    // Naviga alla pagina del cantiere specifico\n    router.push(`/cantieri/${cantiere.id_cantiere}`)\n  }\n\n  const openEditDialog = (cantiere: Cantiere) => {\n    setSelectedCantiere(cantiere)\n    setFormData({\n      commessa: cantiere.commessa || '',\n      descrizione: cantiere.descrizione || '',\n      nome_cliente: cantiere.nome_cliente || '',\n      indirizzo_cantiere: cantiere.indirizzo_cantiere || '',\n      citta_cantiere: cantiere.citta_cantiere || '',\n      nazione_cantiere: cantiere.nazione_cantiere || '',\n      password_cantiere: cantiere.password_cantiere || '',\n      codice_univoco: cantiere.codice_univoco || ''\n    })\n    setShowEditDialog(true)\n  }\n\n  const handleRecoverPasswordDirect = async () => {\n    if (!selectedCantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Chiamata API reale per recupero diretto password\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/view-password`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('access_token')}`\n        }\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.detail || 'Errore nel recupero password')\n      }\n\n      const data = await response.json()\n      setRevealedPassword(data.password_cantiere)\n      setShowRevealedPassword(true)\n      setError('')\n\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Errore nel recupero password')\n      setShowRevealedPassword(false)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleViewPasswordDirect = async (cantiere: Cantiere) => {\n    try {\n      setViewPasswordLoading(true)\n      setError('')\n\n      // Chiamata API per visualizzare la password\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n      const token = localStorage.getItem('token') || localStorage.getItem('access_token')\n      const response = await fetch(`${backendUrl}/api/cantieri/${cantiere.id_cantiere}/view-password`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.detail || 'Errore nel recupero password')\n      }\n\n      const data = await response.json()\n      setSelectedCantiere(cantiere)\n      setRevealedPassword(data.password_cantiere)\n      setShowViewPasswordDialog(true)\n      setError('')\n\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Errore nel recupero password')\n    } finally {\n      setViewPasswordLoading(false)\n    }\n  }\n\n\n\n  const handleChangePassword = async () => {\n    if (!selectedCantiere) return\n\n    if (passwordData.newPassword !== passwordData.confirmPassword) {\n      setError('Le password non coincidono')\n      return\n    }\n\n    if (!passwordData.currentPassword) {\n      setError('Inserisci la password attuale per confermare il cambio')\n      return\n    }\n\n    if (!passwordData.newPassword || passwordData.newPassword.length < 6) {\n      setError('La nuova password deve essere di almeno 6 caratteri')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Chiamata API reale per cambio password\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/change-password`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('access_token')}`\n        },\n        body: JSON.stringify({\n          password_attuale: passwordData.currentPassword,\n          password_nuova: passwordData.newPassword,\n          conferma_password: passwordData.confirmPassword\n        })\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.detail || 'Errore nel cambio password')\n      }\n\n      const data = await response.json()\n\n      if (data.success) {\n        setPasswordData({\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: ''\n        })\n        setShowPasswordDialog(false)\n        setError('')\n        alert(data.message || 'Password cambiata con successo')\n      } else {\n        throw new Error(data.message || 'Errore nel cambio password')\n      }\n\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Errore nel cambio password')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const copyToClipboard = async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      // Visual feedback could be added here (toast notification)\n    } catch (err) {\n    }\n  }\n\n  const togglePasswordVisibility = async (cantiere: Cantiere) => {\n    const cantiereId = cantiere.id_cantiere\n    const isCurrentlyVisible = passwordVisibility[cantiereId]\n\n    if (isCurrentlyVisible) {\n      // Nascondi la password\n      setPasswordVisibility(prev => ({ ...prev, [cantiereId]: false }))\n      setRevealedPasswords(prev => ({ ...prev, [cantiereId]: '' }))\n    } else {\n      // Mostra la password - carica se non già caricata\n      if (!revealedPasswords[cantiereId]) {\n        try {\n          setViewPasswordLoading(true)\n          const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n          const token = localStorage.getItem('token') || localStorage.getItem('access_token')\n          const response = await fetch(`${backendUrl}/api/cantieri/${cantiereId}/view-password`, {\n            method: 'GET',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            }\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.detail || 'Errore nel recupero password')\n          }\n\n          const data = await response.json()\n          setRevealedPasswords(prev => ({ ...prev, [cantiereId]: data.password_cantiere }))\n          setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))\n        } catch (error) {\n          setError(error instanceof Error ? error.message : 'Errore nel recupero password')\n        } finally {\n          setViewPasswordLoading(false)\n        }\n      } else {\n        // Password già caricata, mostra semplicemente\n        setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))\n      }\n    }\n  }\n\n  const filteredCantieri = cantieri.filter(cantiere =>\n    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-[90%] mx-auto p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center gap-4\">\n          <div className=\"relative w-80\">\n            <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Cerca per commessa, descrizione o cliente...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-8 w-full\"\n            />\n          </div>\n        </div>\n        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n          <DialogTrigger asChild>\n            <Button className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">\n              <Plus className=\"mr-2 h-4 w-4\" />\n              Nuovo Cantiere\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"sm:max-w-[425px]\">\n            <DialogHeader>\n              <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n              <DialogDescription>\n                Inserisci i dettagli del nuovo cantiere\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"grid gap-4 py-4\">\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"commessa\" className=\"text-right\">\n                  Commessa\n                </Label>\n                <Input\n                  id=\"commessa\"\n                  value={formData.commessa}\n                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"descrizione\" className=\"text-right\">\n                  Descrizione\n                </Label>\n                <Input\n                  id=\"descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"nome_cliente\" className=\"text-right\">\n                  Cliente\n                </Label>\n                <Input\n                  id=\"nome_cliente\"\n                  value={formData.nome_cliente}\n                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"password_cantiere\" className=\"text-right\">\n                  Password\n                </Label>\n                <Input\n                  id=\"password_cantiere\"\n                  type=\"password\"\n                  value={formData.password_cantiere}\n                  onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n            </div>\n            <DialogFooter>\n              <Button onClick={handleCreateCantiere} className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">Crea Cantiere</Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 p-4 border border-red-200 rounded-lg bg-red-50\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-4 w-4 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {loading ? (\n        <div className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"h-8 w-8 animate-spin\" />\n        </div>\n      ) : filteredCantieri.length === 0 ? (\n        <Card>\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Building2 className=\"h-12 w-12 text-muted-foreground mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Nessun cantiere trovato</h3>\n            <p className=\"text-muted-foreground text-center mb-4\">\n              {searchTerm ? 'Nessun cantiere corrisponde ai criteri di ricerca' : 'Crea il tuo primo cantiere per iniziare'}\n            </p>\n            {!searchTerm && (\n              <Button\n                onClick={() => setShowCreateDialog(true)}\n                className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n              >\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Crea Primo Cantiere\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <Table>\n            <TableHeader>\n              <TableRow className=\"border-b border-gray-200\">\n                <TableHead className=\"font-semibold text-gray-700\">Commessa</TableHead>\n                <TableHead className=\"font-semibold text-gray-700\">Descrizione</TableHead>\n                <TableHead className=\"font-semibold text-gray-700\">Cliente</TableHead>\n                <TableHead className=\"font-semibold text-gray-700\">Data Creazione</TableHead>\n                <TableHead className=\"font-semibold text-gray-700\">Codice Accesso</TableHead>\n                <TableHead className=\"font-semibold text-gray-700\">Password Cantiere</TableHead>\n                <TableHead className=\"font-semibold text-gray-700\">Avanzamento</TableHead>\n                <TableHead className=\"font-semibold text-gray-700 text-center\">Progresso %</TableHead>\n                <TableHead className=\"text-center font-semibold text-gray-700\">Azioni</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredCantieri.map((cantiere) => (\n                <TableRow key={cantiere.id_cantiere} className=\"hover:bg-gray-50/50 transition-colors\">\n                  <TableCell className=\"font-semibold text-gray-900 py-4\">{cantiere.commessa}</TableCell>\n                  <TableCell className=\"text-gray-700 py-4\">{cantiere.descrizione}</TableCell>\n                  <TableCell className=\"text-gray-700 py-4\">{cantiere.nome_cliente}</TableCell>\n                  <TableCell className=\"text-gray-600 py-4\">{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>\n                  <TableCell className=\"py-4\">\n                    <div className=\"flex items-center gap-2\">\n                      <code className=\"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200\">\n                        {cantiere.codice_univoco}\n                      </code>\n                      <Button\n                        size=\"sm\"\n                        variant=\"ghost\"\n                        className=\"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors\"\n                        title=\"Copia codice\"\n                        onClick={() => copyToClipboard(cantiere.codice_univoco)}\n                      >\n                        <Copy className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </TableCell>\n                  <TableCell className=\"py-4\">\n                    <div className=\"flex items-center gap-3\">\n                      {/* Status e Password - Assumiamo che tutti i cantieri abbiano una password configurata */}\n                      <div className=\"flex items-center gap-2\">\n                        {passwordVisibility[cantiere.id_cantiere] && revealedPasswords[cantiere.id_cantiere] ? (\n                          // Password visibile\n                          <div className=\"flex items-center gap-2\">\n                            <code className=\"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono\">\n                              {revealedPasswords[cantiere.id_cantiere]}\n                            </code>\n                            <Button\n                              size=\"sm\"\n                              variant=\"ghost\"\n                              className=\"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors\"\n                              title=\"Nascondi password\"\n                              onClick={() => togglePasswordVisibility(cantiere)}\n                            >\n                              <EyeOff className=\"h-4 w-4\" />\n                            </Button>\n                          </div>\n                        ) : (\n                          // Password nascosta\n                          <div className=\"flex items-center gap-2\">\n                            <div className=\"flex items-center gap-2\">\n                              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                              <span className=\"text-sm text-gray-600 font-medium\">Configurata</span>\n                            </div>\n                            <Button\n                              size=\"sm\"\n                              variant=\"ghost\"\n                              className=\"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors\"\n                              title=\"Mostra password\"\n                              onClick={() => togglePasswordVisibility(cantiere)}\n                              disabled={viewPasswordLoading}\n                            >\n                              {viewPasswordLoading ? (\n                                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                              ) : (\n                                <Eye className=\"h-4 w-4\" />\n                              )}\n                            </Button>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </TableCell>\n                  <TableCell className=\"py-4\">\n                    <div className=\"flex items-center gap-2\">\n                      {statsLoading ? (\n                        <div className=\"flex items-center gap-2\">\n                          <Loader2 className=\"h-4 w-4 animate-spin text-gray-400\" />\n                          <span className=\"text-sm text-gray-500\">Caricamento...</span>\n                        </div>\n                      ) : (\n                        <>\n                          {/* Barra di Progresso Migliorata */}\n                          <div className=\"flex-1 min-w-[120px]\">\n                            <div className=\"w-full bg-gray-200 rounded-full h-3 shadow-inner\">\n                              <div\n                                className={`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${\n                                  (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 90\n                                    ? 'bg-gradient-to-r from-green-500 to-green-600'\n                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 75\n                                    ? 'bg-gradient-to-r from-blue-500 to-blue-600'\n                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 50\n                                    ? 'bg-gradient-to-r from-yellow-500 to-yellow-600'\n                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 25\n                                    ? 'bg-gradient-to-r from-orange-500 to-orange-600'\n                                    : 'bg-gradient-to-r from-red-500 to-red-600'\n                                }`}\n                                style={{\n                                  width: `${Math.min(cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0, 100)}%`\n                                }}\n                              ></div>\n                            </div>\n                          </div>\n                        </>\n                      )}\n                    </div>\n                  </TableCell>\n                  <TableCell className=\"py-4 text-center\">\n                    {statsLoading ? (\n                      <Loader2 className=\"h-4 w-4 animate-spin text-gray-400 mx-auto\" />\n                    ) : (\n                      <div className=\"flex items-center justify-center gap-1\">\n                        <BarChart3 className=\"h-4 w-4 text-gray-500\" />\n                        <span className={`text-sm font-semibold ${\n                          (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 90\n                            ? 'text-green-700'\n                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 75\n                            ? 'text-blue-700'\n                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 50\n                            ? 'text-yellow-700'\n                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 25\n                            ? 'text-orange-700'\n                            : 'text-red-700'\n                        }`}>\n                          {(cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0).toFixed(1)}%\n                        </span>\n                      </div>\n                    )}\n                  </TableCell>\n                  <TableCell className=\"text-center py-4\">\n                    <div className=\"flex items-center justify-center gap-2\">\n                      {/* Pulsante Modifica Cantiere */}\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => openEditDialog(cantiere)}\n                        className=\"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out\"\n                        title=\"Modifica dati cantiere\"\n                      >\n                        <Edit className=\"h-4 w-4 mr-1.5\" />\n                        Modifica\n                      </Button>\n\n                      {/* Pulsante Principale - Accedi al Cantiere */}\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleSelectCantiere(cantiere)}\n                        className=\"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200 ease-in-out shadow-sm hover:shadow-md hover:shadow-blue-500/25 transform hover:scale-105\"\n                        title=\"Accedi al cantiere\"\n                      >\n                        <ArrowRight className=\"h-4 w-4 mr-1.5\" />\n                        Accedi\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </Card>\n      )}\n\n      {/* Dialog di modifica */}\n      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>\n        <DialogContent className=\"sm:max-w-[425px]\">\n          <DialogHeader>\n            <DialogTitle>Modifica Cantiere</DialogTitle>\n            <DialogDescription>\n              Modifica i dettagli del cantiere selezionato\n            </DialogDescription>\n          </DialogHeader>\n          <div className=\"grid gap-4 py-4\">\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-commessa\" className=\"text-right\">\n                Commessa\n              </Label>\n              <Input\n                id=\"edit-commessa\"\n                value={formData.commessa}\n                onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-descrizione\" className=\"text-right\">\n                Descrizione\n              </Label>\n              <Input\n                id=\"edit-descrizione\"\n                value={formData.descrizione}\n                onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-nome_cliente\" className=\"text-right\">\n                Cliente\n              </Label>\n              <Input\n                id=\"edit-nome_cliente\"\n                value={formData.nome_cliente}\n                onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-indirizzo_cantiere\" className=\"text-right\">\n                Indirizzo\n              </Label>\n              <Input\n                id=\"edit-indirizzo_cantiere\"\n                value={formData.indirizzo_cantiere}\n                onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-citta_cantiere\" className=\"text-right\">\n                Città\n              </Label>\n              <Input\n                id=\"edit-citta_cantiere\"\n                value={formData.citta_cantiere}\n                onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-nazione_cantiere\" className=\"text-right\">\n                Nazione\n              </Label>\n              <Input\n                id=\"edit-nazione_cantiere\"\n                value={formData.nazione_cantiere}\n                onChange={(e) => setFormData({ ...formData, nazione_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label className=\"text-right\">\n                Password\n              </Label>\n              <div className=\"col-span-3\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowPasswordDialog(true)\n                  }}\n                  className=\"w-full h-10 justify-start text-left bg-gray-50 hover:bg-gray-100 border-gray-200 hover:border-gray-300 transition-colors\"\n                >\n                  <Lock className=\"h-4 w-4 mr-2 text-gray-500\" />\n                  <span className=\"text-gray-700\">Modifica Password</span>\n                </Button>\n              </div>\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setShowEditDialog(false)} className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">\n              Annulla\n            </Button>\n            <Button onClick={handleEditCantiere} className=\"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\">Salva Modifiche</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog gestione password ottimale */}\n      <Dialog open={showPasswordDialog} onOpenChange={(open) => {\n        setShowPasswordDialog(open)\n        if (!open) {\n          setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' })\n          setError('')\n        }\n      }}>\n        <DialogContent className=\"sm:max-w-[600px]\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Lock className=\"h-5 w-5\" />\n              Gestione Password - {selectedCantiere?.commessa}\n            </DialogTitle>\n            <DialogDescription>\n              Modifica la password di accesso al cantiere\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-6\">\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-medium flex items-center gap-2\">\n                <Settings className=\"h-5 w-5\" />\n                Cambia Password\n              </h3>\n              <p className=\"text-sm text-gray-600\">\n                Inserisci la password attuale e la nuova password\n              </p>\n                <div className=\"space-y-3\">\n                  <div>\n                    <Label htmlFor=\"current-password-change\">Password Attuale</Label>\n                    <Input\n                      id=\"current-password-change\"\n                      type=\"password\"\n                      placeholder=\"Password attuale per conferma\"\n                      value={passwordData.currentPassword}\n                      onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"new-password\">Nuova Password</Label>\n                    <Input\n                      id=\"new-password\"\n                      type=\"password\"\n                      placeholder=\"Inserisci la nuova password\"\n                      value={passwordData.newPassword}\n                      onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"confirm-password\">Conferma Nuova Password</Label>\n                    <Input\n                      id=\"confirm-password\"\n                      type=\"password\"\n                      placeholder=\"Conferma la nuova password\"\n                      value={passwordData.confirmPassword}\n                      onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}\n                    />\n                  </div>\n                  <Button\n                    onClick={handleChangePassword}\n                    disabled={loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}\n                    className=\"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]\"\n                  >\n                    {loading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Settings className=\"mr-2 h-4 w-4\" />}\n                    Cambia Password\n                  </Button>\n                </div>\n              </div>\n\n            {/* Messaggio di errore */}\n            {error && (\n              <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n                <div className=\"flex items-center gap-2\">\n                  <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                  <span className=\"font-medium text-red-800\">Errore</span>\n                </div>\n                <p className=\"text-sm text-red-700 mt-1\">{error}</p>\n              </div>\n            )}\n          </div>\n\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowPasswordDialog(false)}\n            >\n              Chiudi\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog visualizzazione password semplice */}\n      <Dialog open={showViewPasswordDialog} onOpenChange={(open) => {\n        setShowViewPasswordDialog(open)\n        if (!open) {\n          setRevealedPassword('')\n          setSelectedCantiere(null)\n          setError('')\n        }\n      }}>\n        <DialogContent className=\"sm:max-w-[500px]\">\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Eye className=\"h-5 w-5 text-green-600\" />\n              Password Cantiere - {selectedCantiere?.commessa}\n            </DialogTitle>\n            <DialogDescription>\n              Password per l'accesso al cantiere con codice: <code className=\"bg-muted px-2 py-1 rounded\">{selectedCantiere?.codice_univoco}</code>\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-4\">\n            {revealedPassword && (\n              <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n                <div className=\"flex items-center gap-2 mb-3\">\n                  <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                  <span className=\"font-medium text-green-800\">Password del Cantiere</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <code className=\"flex-1 text-lg font-mono bg-white p-3 rounded border border-green-300 text-center\">\n                    {revealedPassword}\n                  </code>\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={() => copyToClipboard(revealedPassword)}\n                    className=\"text-green-600 hover:bg-green-50 border-green-300\"\n                    title=\"Copia password\"\n                  >\n                    <Copy className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n                <p className=\"text-sm text-green-700 mt-2\">\n                  Utilizza questa password insieme al codice univoco <strong>{selectedCantiere?.codice_univoco}</strong> per accedere al cantiere.\n                </p>\n              </div>\n            )}\n\n            {error && (\n              <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\n                <div className=\"flex items-center gap-2\">\n                  <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                  <span className=\"font-medium text-red-800\">Errore</span>\n                </div>\n                <p className=\"text-sm text-red-700 mt-1\">{error}</p>\n              </div>\n            )}\n          </div>\n\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowViewPasswordDialog(false)}\n            >\n              Chiudi\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;AAkMyB;;AAhMzB;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;;;;AAqCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuD,CAAC;IACzG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAGjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;IAClB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,iBAAiB;QACjB,aAAa;QACb,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACvF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEpF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB;gBACnB;YACF;QACF;iCAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,WAAW;YAC1C,YAAY;YAEZ,0CAA0C;YAC1C,MAAM,uBAAuB;QAC/B,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,gBAAgB;YAChB,MAAM,gBAAgB,aAAa,GAAG,CAAC,OAAO;gBAC5C,IAAI;oBACF,MAAM,QAAQ,MAAM,oHAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC,SAAS,WAAW;oBAC1E,OAAO;wBAAE,IAAI,SAAS,WAAW;wBAAE;oBAAM;gBAC3C,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,4CAA4C,EAAE,SAAS,WAAW,CAAC,CAAC,CAAC,EAAE;oBACtF,OAAO;wBAAE,IAAI,SAAS,WAAW;wBAAE,OAAO;4BAAE,yBAAyB;wBAAE;oBAAE;gBAC3E;YACF;YAEA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE;gBACjD,GAAG,CAAC,GAAG,GAAG;gBACV,OAAO;YACT,GAAG,CAAC;YAEJ,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;QAC7D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YACjC,oBAAoB;YACpB,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc;gBACd,oBAAoB;gBACpB,gBAAgB;gBAChB,kBAAkB;gBAClB,mBAAmB;gBACnB,gBAAgB;YAClB;YACA;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,iBAAiB,WAAW,EAAE;YAC/D,kBAAkB;YAClB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iDAAiD;QACjD,aAAa,OAAO,CAAC,sBAAsB,SAAS,WAAW,CAAC,QAAQ;QACxE,aAAa,OAAO,CAAC,wBAAwB,SAAS,QAAQ;QAE9D,4CAA4C;QAC5C,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,WAAW,EAAE;IACjD;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB;QACpB,YAAY;YACV,UAAU,SAAS,QAAQ,IAAI;YAC/B,aAAa,SAAS,WAAW,IAAI;YACrC,cAAc,SAAS,YAAY,IAAI;YACvC,oBAAoB,SAAS,kBAAkB,IAAI;YACnD,gBAAgB,SAAS,cAAc,IAAI;YAC3C,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,mBAAmB,SAAS,iBAAiB,IAAI;YACjD,gBAAgB,SAAS,cAAc,IAAI;QAC7C;QACA,kBAAkB;IACpB;IAEA,MAAM,8BAA8B;QAClC,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,WAAW;YACX,SAAS;YAET,mDAAmD;YACnD,MAAM,aAAa,6DAAmC;YACtD,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,cAAc,EAAE,iBAAiB,WAAW,CAAC,cAAc,CAAC,EAAE;gBACvG,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACnE;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,oBAAoB,KAAK,iBAAiB;YAC1C,wBAAwB;YACxB,SAAS;QAEX,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,wBAAwB;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI;YACF,uBAAuB;YACvB,SAAS;YAET,4CAA4C;YAC5C,MAAM,aAAa,6DAAmC;YACtD,MAAM,QAAQ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;YACpE,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,cAAc,EAAE,SAAS,WAAW,CAAC,cAAc,CAAC,EAAE;gBAC/F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,oBAAoB;YACpB,oBAAoB,KAAK,iBAAiB;YAC1C,0BAA0B;YAC1B,SAAS;QAEX,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,uBAAuB;QACzB;IACF;IAIA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,kBAAkB;QAEvB,IAAI,aAAa,WAAW,KAAK,aAAa,eAAe,EAAE;YAC7D,SAAS;YACT;QACF;QAEA,IAAI,CAAC,aAAa,eAAe,EAAE;YACjC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,aAAa,WAAW,IAAI,aAAa,WAAW,CAAC,MAAM,GAAG,GAAG;YACpE,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,yCAAyC;YACzC,MAAM,aAAa,6DAAmC;YACtD,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,cAAc,EAAE,iBAAiB,WAAW,CAAC,gBAAgB,CAAC,EAAE;gBACzG,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,iBAAiB;gBACnE;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,kBAAkB,aAAa,eAAe;oBAC9C,gBAAgB,aAAa,WAAW;oBACxC,mBAAmB,aAAa,eAAe;gBACjD;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB;oBACd,iBAAiB;oBACjB,aAAa;oBACb,iBAAiB;gBACnB;gBACA,sBAAsB;gBACtB,SAAS;gBACT,MAAM,KAAK,OAAO,IAAI;YACxB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;QAEF,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,2DAA2D;QAC7D,EAAE,OAAO,KAAK,CACd;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,MAAM,aAAa,SAAS,WAAW;QACvC,MAAM,qBAAqB,kBAAkB,CAAC,WAAW;QAEzD,IAAI,oBAAoB;YACtB,uBAAuB;YACvB,sBAAsB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,WAAW,EAAE;gBAAM,CAAC;YAC/D,qBAAqB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,WAAW,EAAE;gBAAG,CAAC;QAC7D,OAAO;YACL,kDAAkD;YAClD,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;gBAClC,IAAI;oBACF,uBAAuB;oBACvB,MAAM,aAAa,6DAAmC;oBACtD,MAAM,QAAQ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;oBACpE,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,cAAc,EAAE,WAAW,cAAc,CAAC,EAAE;wBACrF,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;wBACpC;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;wBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;oBACtC;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,qBAAqB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,WAAW,EAAE,KAAK,iBAAiB;wBAAC,CAAC;oBAC/E,sBAAsB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,WAAW,EAAE;wBAAK,CAAC;gBAChE,EAAE,OAAO,OAAO;oBACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACpD,SAAU;oBACR,uBAAuB;gBACzB;YACF,OAAO;gBACL,8CAA8C;gBAC9C,sBAAsB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,WAAW,EAAE;oBAAK,CAAC;YAChE;QACF;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,WACvC,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,SAAS,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OACnE,SAAS,YAAY,EAAE,cAAc,SAAS,WAAW,WAAW;IAGtE,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;kCAIhB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAkB,cAAc;;0CAC5C,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAa;;;;;;kEAGjD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAc,WAAU;kEAAa;;;;;;kEAGpD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAe,WAAU;kEAAa;;;;;;kEAGrD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,YAAY;wDAC5B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACzE,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAoB,WAAU;kEAAa;;;;;;kEAG1D,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,iBAAiB;wDACjC,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC9E,WAAU;;;;;;;;;;;;;;;;;;kDAIhB,6LAAC,qIAAA,CAAA,eAAY;kDACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAsB,WAAU;sDAA6a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMre,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAKrC,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;uBAEnB,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCACV,aAAa,sDAAsD;;;;;;wBAErE,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,oBAAoB;4BACnC,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;qCAOzC,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA8B;;;;;;kDACnD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA8B;;;;;;kDACnD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA8B;;;;;;kDACnD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA8B;;;;;;kDACnD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA8B;;;;;;kDACnD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA8B;;;;;;kDACnD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA8B;;;;;;kDACnD,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA0C;;;;;;kDAC/D,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA0C;;;;;;;;;;;;;;;;;sCAGnE,6LAAC,oIAAA,CAAA,YAAS;sCACP,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC,oIAAA,CAAA,WAAQ;oCAA4B,WAAU;;sDAC7C,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAoC,SAAS,QAAQ;;;;;;sDAC1E,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB,SAAS,WAAW;;;;;;sDAC/D,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB,SAAS,YAAY;;;;;;sDAChE,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB,IAAI,KAAK,SAAS,cAAc,EAAE,kBAAkB;;;;;;sDAC/F,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,SAAS,cAAc;;;;;;kEAE1B,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;wDACV,OAAM;wDACN,SAAS,IAAM,gBAAgB,SAAS,cAAc;kEAEtD,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAItB,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC;gDAAI,WAAU;0DAEb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,kBAAkB,CAAC,SAAS,WAAW,CAAC,IAAI,iBAAiB,CAAC,SAAS,WAAW,CAAC,GAClF,oBAAoB;kEACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb,iBAAiB,CAAC,SAAS,WAAW,CAAC;;;;;;0EAE1C,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,OAAM;gEACN,SAAS,IAAM,yBAAyB;0EAExC,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;+DAItB,oBAAoB;kEACpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;;0EAEtD,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,OAAM;gEACN,SAAS,IAAM,yBAAyB;gEACxC,UAAU;0EAET,oCACC,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ7B,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC;gDAAI,WAAU;0DACZ,6BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;yEAG1C;8DAEE,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAW,CAAC,gEAAgE,EAC1E,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACnE,iDACA,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACvE,+CACA,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACvE,mDACA,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACvE,mDACA,4CACJ;gEACF,OAAO;oEACL,OAAO,GAAG,KAAK,GAAG,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,GAAG,KAAK,CAAC,CAAC;gEAC/F;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQd,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,6BACC,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAW,CAAC,sBAAsB,EACtC,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACnE,mBACA,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACvE,kBACA,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACvE,oBACA,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,KAAK,KACvE,oBACA,gBACJ;;4DACC,CAAC,aAAa,CAAC,SAAS,WAAW,CAAC,EAAE,2BAA2B,CAAC,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;sDAKxF,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,eAAe;wDAC9B,WAAU;wDACV,OAAM;;0EAEN,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAmB;;;;;;;kEAKrC,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,qBAAqB;wDACpC,WAAU;wDACV,OAAM;;0EAEN,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAmB;;;;;;;;;;;;;;;;;;;mCAhJlC,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;0BA6J7C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAa;;;;;;sDAGtD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrE,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAmB,WAAU;sDAAa;;;;;;sDAGzD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAoB,WAAU;sDAAa;;;;;;sDAG1D,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,YAAY;4CAC5B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAA0B,WAAU;sDAAa;;;;;;sDAGhE,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,kBAAkB;4CAClC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/E,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAsB,WAAU;sDAAa;;;;;;sDAG5D,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC3E,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAwB,WAAU;sDAAa;;;;;;sDAG9D,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,gBAAgB;4CAChC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC7E,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAa;;;;;;sDAG9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;oDACP,sBAAsB;gDACxB;gDACA,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKxC,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,kBAAkB;oCAAQ,WAAU;8CAA6a;;;;;;8CAGxe,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,WAAU;8CAA6a;;;;;;;;;;;;;;;;;;;;;;;0BAMle,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAoB,cAAc,CAAC;oBAC/C,sBAAsB;oBACtB,IAAI,CAAC,MAAM;wBACT,gBAAgB;4BAAE,iBAAiB;4BAAI,aAAa;4BAAI,iBAAiB;wBAAG;wBAC5E,SAAS;oBACX;gBACF;0BACE,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;wCACP,kBAAkB;;;;;;;8CAEzC,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGlC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDAGnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAA0B;;;;;;sEACzC,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,OAAO,aAAa,eAAe;4DACnC,UAAU,CAAC,IAAM,gBAAgB;oEAAE,GAAG,YAAY;oEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAAC;;;;;;;;;;;;8DAGxF,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,OAAO,aAAa,WAAW;4DAC/B,UAAU,CAAC,IAAM,gBAAgB;oEAAE,GAAG,YAAY;oEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gEAAC;;;;;;;;;;;;8DAGpF,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,aAAY;4DACZ,OAAO,aAAa,eAAe;4DACnC,UAAU,CAAC,IAAM,gBAAgB;oEAAE,GAAG,YAAY;oEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAAC;;;;;;;;;;;;8DAGxF,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,WAAW,CAAC,aAAa,eAAe,IAAI,CAAC,aAAa,WAAW,IAAI,CAAC,aAAa,eAAe;oDAChH,WAAU;;wDAET,wBAAU,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAAiC,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAkB;;;;;;;;;;;;;;;;;;;gCAO5G,uBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;sDAE7C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAKhD,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,sBAAsB;0CACtC;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAwB,cAAc,CAAC;oBACnD,0BAA0B;oBAC1B,IAAI,CAAC,MAAM;wBACT,oBAAoB;wBACpB,oBAAoB;wBACpB,SAAS;oBACX;gBACF;0BACE,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAA2B;wCACrB,kBAAkB;;;;;;;8CAEzC,6LAAC,qIAAA,CAAA,oBAAiB;;wCAAC;sDAC8B,6LAAC;4CAAK,WAAU;sDAA8B,kBAAkB;;;;;;;;;;;;;;;;;;sCAInH,6LAAC;4BAAI,WAAU;;gCACZ,kCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb;;;;;;8DAEH,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGpB,6LAAC;4CAAE,WAAU;;gDAA8B;8DACU,6LAAC;8DAAQ,kBAAkB;;;;;;gDAAwB;;;;;;;;;;;;;gCAK3G,uBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;sDAE7C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAKhD,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,0BAA0B;0CAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAx4BwB;;QACuB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}