/* [project]/src/styles/admin.css [app-client] (css) */
.admin-tab-trigger {
  position: relative;
  overflow: hidden;
}

.admin-tab-trigger:before {
  content: "";
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  width: 0;
  height: 3px;
  transition: width .3s ease-in-out;
  position: absolute;
  bottom: 0;
  left: 0;
}

.admin-tab-trigger[data-state="active"]:before {
  width: 100%;
}

.admin-tab-trigger:hover:not([data-state="active"]) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #0000001a;
}

.admin-tab-danger {
  position: relative;
  overflow: hidden;
}

.admin-tab-danger:before {
  content: "";
  background: linear-gradient(90deg, #ef4444, #dc2626);
  width: 0;
  height: 3px;
  transition: width .3s ease-in-out;
  position: absolute;
  bottom: 0;
  left: 0;
}

.admin-tab-danger[data-state="active"]:before {
  width: 100%;
}

.admin-tab-danger:hover:not([data-state="active"]) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #ef444433;
}

.tab-content-enter {
  opacity: 0;
  transform: translateY(10px);
}

.tab-content-enter-active {
  opacity: 1;
  transition: opacity .3s ease-in-out, transform .3s ease-in-out;
  transform: translateY(0);
}

.users-table-row {
  transition: all .2s ease-in-out;
}

.users-table-row:hover {
  background-color: #f8fafccc;
  transform: translateX(2px);
  box-shadow: 0 2px 8px #0000000d;
}

.status-badge {
  position: relative;
  overflow: hidden;
}

.status-badge:before {
  content: "";
  background: linear-gradient(90deg, #0000, #ffffff4d, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s ease-in-out;
  position: absolute;
  top: 0;
  left: -100%;
}

.status-badge:hover:before {
  left: 100%;
}

.admin-tab-trigger:focus-visible, .admin-tab-danger:focus-visible {
  outline-offset: 2px;
  border-radius: 6px;
  outline: 2px solid #3b82f6;
}

@media (width <= 768px) {
  .admin-tab-trigger span, .admin-tab-danger span {
    display: none;
  }

  .admin-tab-trigger, .admin-tab-danger {
    justify-content: center;
    min-width: 48px;
  }
}

.section-indicator {
  position: relative;
}

.section-indicator:after {
  content: "";
  opacity: 0;
  background: #10b981;
  border-radius: 50%;
  width: 4px;
  height: 4px;
  transition: opacity .3s ease-in-out;
  position: absolute;
  top: 50%;
  right: -8px;
  transform: translateY(-50%);
}

.section-indicator.active:after {
  opacity: 1;
}

.dropdown-enter {
  opacity: 0;
  transform: scale(.95)translateY(-10px);
}

.dropdown-enter-active {
  opacity: 1;
  transition: opacity .2s ease-out, transform .2s ease-out;
  transform: scale(1)translateY(0);
}

.dropdown-exit {
  opacity: 1;
  transform: scale(1)translateY(0);
}

.dropdown-exit-active {
  opacity: 0;
  transition: opacity .15s ease-in, transform .15s ease-in;
  transform: scale(.95)translateY(-10px);
}

.action-button {
  transition: all .2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.action-button:before {
  content: "";
  background: #ffffff4d;
  border-radius: 50%;
  width: 0;
  height: 0;
  transition: width .3s ease-out, height .3s ease-out;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.action-button:hover:before {
  width: 120%;
  height: 120%;
}

.status-notification {
  animation: .3s ease-out slideInFromTop;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-content {
  letter-spacing: .01em;
  line-height: 1.6;
}

.visual-separator {
  position: relative;
}

.visual-separator:before {
  content: "";
  background: linear-gradient(90deg, #0000, #e2e8f0, #0000);
  width: 50px;
  height: 2px;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.loading-shimmer {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%) 0 0 / 200% 100%;
  animation: 1.5s infinite shimmer;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.high-contrast {
  filter: contrast(1.1);
}

.focus-ring:focus-visible {
  outline-offset: 2px;
  border-radius: 4px;
  outline: 2px solid #3b82f6;
}

.icon-rotate {
  transition: transform .3s ease-in-out;
}

.icon-rotate.active {
  transform: rotate(180deg);
}

.tooltip-content {
  backdrop-filter: blur(8px);
  background: #0f172ae6;
  border: 1px solid #ffffff1a;
}

.skip-link {
  z-index: 50;
  color: #fff;
  background: #1d4ed8;
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  text-decoration: none;
  transition: top .2s ease-in-out;
  position: absolute;
  top: -40px;
  left: 16px;
}

.skip-link:focus {
  top: 16px;
}

.focus-trap {
  position: relative;
}

.focus-trap:before, .focus-trap:after {
  content: "";
  opacity: 0;
  pointer-events: none;
  width: 1px;
  height: 1px;
  position: absolute;
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.text-high-contrast {
  color: #0f172a;
  font-weight: 500;
}

.text-medium-contrast {
  color: #334155;
  font-weight: 400;
}

.error-state {
  color: #991b1b;
  background-color: #fef2f2;
  border: 2px solid #dc2626;
}

.error-state:focus {
  outline-offset: 2px;
  outline: 2px solid #dc2626;
}

.success-state {
  color: #15803d;
  background-color: #f0fdf4;
  border: 2px solid #16a34a;
}

.success-state:focus {
  outline-offset: 2px;
  outline: 2px solid #16a34a;
}

.keyboard-navigation:focus-within {
  outline-offset: 2px;
  border-radius: 6px;
  outline: 2px solid #3b82f6;
}

.progress-indicator {
  background: #e2e8f0;
  border-radius: 9999px;
  position: relative;
  overflow: hidden;
}

.progress-indicator:after {
  content: attr(aria-valuenow) "%";
  color: #475569;
  font-size: 12px;
  font-weight: 600;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.progress-bar {
  border-radius: inherit;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  height: 100%;
  transition: width .5s ease-in-out;
}

.modal-overlay {
  backdrop-filter: blur(4px);
  z-index: 40;
  background: #00000080;
  position: fixed;
  inset: 0;
}

.modal-content {
  background: #fff;
  border-radius: 8px;
  max-height: 90vh;
  position: relative;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px #0000001a, 0 10px 10px -5px #0000000a;
}

.modal-content:focus {
  outline-offset: -2px;
  outline: 2px solid #3b82f6;
}

@media (prefers-reduced-motion: reduce) {
  *, :before, :after {
    transition-duration: .01ms !important;
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
  }
}

@media (prefers-color-scheme: dark) {
  .auto-theme {
    color: #f1f5f9;
    background-color: #0f172a;
  }

  .auto-theme .admin-card {
    background-color: #1e293b;
    border-color: #334155;
  }

  .auto-theme .admin-input {
    color: #f1f5f9;
    background-color: #1e293b;
    border-color: #334155;
  }

  .auto-theme .admin-input::placeholder {
    color: #64748b;
  }
}

@media print {
  .no-print {
    display: none !important;
  }

  .admin-card {
    box-shadow: none;
    border: 1px solid #000;
  }

  .admin-button {
    color: #000;
    background: #fff;
    border: 1px solid #000;
  }
}


/*# sourceMappingURL=src_styles_admin_e5b39fc0.css.map*/