{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/styles/admin.css"], "sourcesContent": ["/* Stili personalizzati per il pannello di amministrazione */\n\n/* Tab triggers migliorati */\n.admin-tab-trigger {\n  position: relative;\n  overflow: hidden;\n}\n\n.admin-tab-trigger::before {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #3b82f6, #1d4ed8);\n  transition: width 0.3s ease-in-out;\n}\n\n.admin-tab-trigger[data-state=\"active\"]::before {\n  width: 100%;\n}\n\n.admin-tab-trigger:hover:not([data-state=\"active\"]) {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* Tab pericolosi */\n.admin-tab-danger {\n  position: relative;\n  overflow: hidden;\n}\n\n.admin-tab-danger::before {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #ef4444, #dc2626);\n  transition: width 0.3s ease-in-out;\n}\n\n.admin-tab-danger[data-state=\"active\"]::before {\n  width: 100%;\n}\n\n.admin-tab-danger:hover:not([data-state=\"active\"]) {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);\n}\n\n/* Animazioni per i contenuti delle tab */\n.tab-content-enter {\n  opacity: 0;\n  transform: translateY(10px);\n}\n\n.tab-content-enter-active {\n  opacity: 1;\n  transform: translateY(0);\n  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;\n}\n\n/* Miglioramenti per la tabella utenti */\n.users-table-row {\n  transition: all 0.2s ease-in-out;\n}\n\n.users-table-row:hover {\n  background-color: rgba(248, 250, 252, 0.8);\n  transform: translateX(2px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n/* Badge migliorati */\n.status-badge {\n  position: relative;\n  overflow: hidden;\n}\n\n.status-badge::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.5s ease-in-out;\n}\n\n.status-badge:hover::before {\n  left: 100%;\n}\n\n/* Effetti di focus migliorati per accessibilità */\n.admin-tab-trigger:focus-visible,\n.admin-tab-danger:focus-visible {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n  border-radius: 6px;\n}\n\n/* Responsive design per le tab */\n@media (max-width: 768px) {\n  .admin-tab-trigger span,\n  .admin-tab-danger span {\n    display: none;\n  }\n  \n  .admin-tab-trigger,\n  .admin-tab-danger {\n    justify-content: center;\n    min-width: 48px;\n  }\n}\n\n/* Indicatori di stato per le sezioni */\n.section-indicator {\n  position: relative;\n}\n\n.section-indicator::after {\n  content: '';\n  position: absolute;\n  top: 50%;\n  right: -8px;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 4px;\n  background: #10b981;\n  border-radius: 50%;\n  opacity: 0;\n  transition: opacity 0.3s ease-in-out;\n}\n\n.section-indicator.active::after {\n  opacity: 1;\n}\n\n/* Animazioni per i dropdown */\n.dropdown-enter {\n  opacity: 0;\n  transform: scale(0.95) translateY(-10px);\n}\n\n.dropdown-enter-active {\n  opacity: 1;\n  transform: scale(1) translateY(0);\n  transition: opacity 0.2s ease-out, transform 0.2s ease-out;\n}\n\n.dropdown-exit {\n  opacity: 1;\n  transform: scale(1) translateY(0);\n}\n\n.dropdown-exit-active {\n  opacity: 0;\n  transform: scale(0.95) translateY(-10px);\n  transition: opacity 0.15s ease-in, transform 0.15s ease-in;\n}\n\n/* Miglioramenti per i pulsanti di azione */\n.action-button {\n  position: relative;\n  overflow: hidden;\n  transition: all 0.2s ease-in-out;\n}\n\n.action-button::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 0;\n  height: 0;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  transform: translate(-50%, -50%);\n  transition: width 0.3s ease-out, height 0.3s ease-out;\n}\n\n.action-button:hover::before {\n  width: 120%;\n  height: 120%;\n}\n\n/* Stili per le notifiche di stato */\n.status-notification {\n  animation: slideInFromTop 0.3s ease-out;\n}\n\n@keyframes slideInFromTop {\n  from {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Miglioramenti per la leggibilità */\n.admin-content {\n  line-height: 1.6;\n  letter-spacing: 0.01em;\n}\n\n/* Separatori visivi */\n.visual-separator {\n  position: relative;\n}\n\n.visual-separator::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 50px;\n  height: 2px;\n  background: linear-gradient(90deg, transparent, #e2e8f0, transparent);\n}\n\n/* Effetti di caricamento */\n.loading-shimmer {\n  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* Miglioramenti per il contrasto e l'accessibilità */\n.high-contrast {\n  filter: contrast(1.1);\n}\n\n.focus-ring:focus-visible {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n  border-radius: 4px;\n}\n\n/* Stili per le icone animate */\n.icon-rotate {\n  transition: transform 0.3s ease-in-out;\n}\n\n.icon-rotate.active {\n  transform: rotate(180deg);\n}\n\n/* Miglioramenti per i tooltip */\n.tooltip-content {\n  background: rgba(15, 23, 42, 0.9);\n  backdrop-filter: blur(8px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n/* ===== WCAG 2.1 AA COMPLIANCE ENHANCEMENTS ===== */\n\n/* Skip link per navigazione da tastiera */\n.skip-link {\n  position: absolute;\n  top: -40px;\n  left: 16px;\n  z-index: 50;\n  background: #1d4ed8;\n  color: white;\n  padding: 8px 16px;\n  border-radius: 4px;\n  text-decoration: none;\n  font-weight: 500;\n  transition: top 0.2s ease-in-out;\n}\n\n.skip-link:focus {\n  top: 16px;\n}\n\n/* Focus management migliorato */\n.focus-trap {\n  position: relative;\n}\n\n.focus-trap::before,\n.focus-trap::after {\n  content: '';\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  opacity: 0;\n  pointer-events: none;\n}\n\n/* Indicatori di stato accessibili */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n/* Contrasti migliorati per testo */\n.text-high-contrast {\n  color: #0f172a;\n  font-weight: 500;\n}\n\n.text-medium-contrast {\n  color: #334155;\n  font-weight: 400;\n}\n\n/* Stati di errore accessibili */\n.error-state {\n  border: 2px solid #dc2626;\n  background-color: #fef2f2;\n  color: #991b1b;\n}\n\n.error-state:focus {\n  outline: 2px solid #dc2626;\n  outline-offset: 2px;\n}\n\n/* Stati di successo accessibili */\n.success-state {\n  border: 2px solid #16a34a;\n  background-color: #f0fdf4;\n  color: #15803d;\n}\n\n.success-state:focus {\n  outline: 2px solid #16a34a;\n  outline-offset: 2px;\n}\n\n/* Miglioramenti per la navigazione da tastiera */\n.keyboard-navigation:focus-within {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n  border-radius: 6px;\n}\n\n/* Indicatori di progresso accessibili */\n.progress-indicator {\n  position: relative;\n  background: #e2e8f0;\n  border-radius: 9999px;\n  overflow: hidden;\n}\n\n.progress-indicator::after {\n  content: attr(aria-valuenow) '%';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 12px;\n  font-weight: 600;\n  color: #475569;\n}\n\n.progress-bar {\n  height: 100%;\n  background: linear-gradient(90deg, #3b82f6, #1d4ed8);\n  border-radius: inherit;\n  transition: width 0.5s ease-in-out;\n}\n\n/* Miglioramenti per i modali accessibili */\n.modal-overlay {\n  position: fixed;\n  inset: 0;\n  background: rgba(0, 0, 0, 0.5);\n  backdrop-filter: blur(4px);\n  z-index: 40;\n}\n\n.modal-content {\n  position: relative;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-content:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: -2px;\n}\n\n/* Animazioni rispettose delle preferenze di movimento */\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n\n/* Miglioramenti per il tema scuro (se implementato) */\n@media (prefers-color-scheme: dark) {\n  .auto-theme {\n    background-color: #0f172a;\n    color: #f1f5f9;\n  }\n\n  .auto-theme .admin-card {\n    background-color: #1e293b;\n    border-color: #334155;\n  }\n\n  .auto-theme .admin-input {\n    background-color: #1e293b;\n    border-color: #334155;\n    color: #f1f5f9;\n  }\n\n  .auto-theme .admin-input::placeholder {\n    color: #64748b;\n  }\n}\n\n/* Miglioramenti per stampa */\n@media print {\n  .no-print {\n    display: none !important;\n  }\n\n  .admin-card {\n    box-shadow: none;\n    border: 1px solid #000;\n  }\n\n  .admin-button {\n    border: 1px solid #000;\n    background: white;\n    color: black;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;;AAKA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAMA;;;;;AAKA;;;;;;AAOA;;;;AAIA;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;AAWA;;;;AAKA;;;;;;AAQA;EACE;;;;EAKA;;;;;;AAQF;;;;AAIA;;;;;;;;;;;;;;AAcA;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAYA;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;;;AASA;;;;;;;;;;;;;;AAcA;;;;AAKA;;;;AAIA;;;;;;;;;AAWA;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAMA;EACE;;;;;;;AAUF;EACE;;;;;EAKA;;;;;EAKA;;;;;;EAMA;;;;;AAMF;EACE;;;;EAIA;;;;;EAKA", "debugId": null}}]}