{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "b0efed62b328d3dd8c360d8666c119da", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bd88bb597cac347b432f9b1f1516578a51735a4e7b2d5be705c94f67933277e9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "feaca5c3367fa21cc1cab25b4da2c7eb4cc0736b7176e90515202cd2ec34b116"}}}, "sortedMiddleware": ["/"], "functions": {}}