{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/animated-button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\nimport { Loader2 } from 'lucide-react'\n\ninterface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'outline' | 'quick'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  glow?: boolean\n  icon?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    loading = false, \n    glow = false,\n    icon,\n    children, \n    disabled,\n    ...props \n  }, ref) => {\n    const baseClasses = 'relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none'\n    \n    const variantClasses = {\n      primary: 'btn-primary',\n      secondary: 'btn-secondary',\n      success: 'btn-success',\n      danger: 'btn-danger',\n      outline: 'btn-outline',\n      quick: 'btn-quick'\n    }\n    \n    const sizeClasses = {\n      sm: 'btn-sm',\n      md: 'px-6 py-3',\n      lg: 'btn-lg'\n    }\n\n    const isDisabled = disabled || loading\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          glow && variant !== 'quick' && 'btn-glow',\n          isDisabled && 'opacity-50 cursor-not-allowed hover:shadow-none',\n          className\n        )}\n        disabled={isDisabled}\n        ref={ref}\n        {...props}\n      >\n        {/* Effetto shimmer per hover */}\n        <span className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]\" />\n        \n        {/* Contenuto del pulsante */}\n        <span className=\"relative flex items-center justify-center gap-2\">\n          {loading ? (\n            <Loader2 className=\"h-4 w-4 animate-spin btn-icon\" />\n          ) : icon ? (\n            <span className=\"btn-icon\">{icon}</span>\n          ) : null}\n          {children}\n        </span>\n      </button>\n    )\n  }\n)\n\nAnimatedButton.displayName = 'AnimatedButton'\n\n// Componenti specifici per facilità d'uso\nexport const PrimaryButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"primary\" {...props} />\n)\n\nexport const SecondaryButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"secondary\" {...props} />\n)\n\nexport const SuccessButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"success\" {...props} />\n)\n\nexport const DangerButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"danger\" {...props} />\n)\n\nexport const OutlineButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"outline\" {...props} />\n)\n\nexport const QuickButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"quick\" {...props} />\n)\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;;AAWO,MAAM,+BAAiB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC5C,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,OAAO,KAAK,EACZ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,aAAa,YAAY;IAE/B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,QAAQ,YAAY,WAAW,YAC/B,cAAc,mDACd;QAEF,UAAU;QACV,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBAAK,WAAU;;;;;;0BAGhB,8OAAC;gBAAK,WAAU;;oBACb,wBACC,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;+BACjB,qBACF,8OAAC;wBAAK,WAAU;kCAAY;;;;;+BAC1B;oBACH;;;;;;;;;;;;;AAIT;AAGF,eAAe,WAAW,GAAG;AAGtB,MAAM,gBAAgB,CAAC,sBAC5B,8OAAC;QAAe,SAAQ;QAAW,GAAG,KAAK;;;;;;AAGtC,MAAM,kBAAkB,CAAC,sBAC9B,8OAAC;QAAe,SAAQ;QAAa,GAAG,KAAK;;;;;;AAGxC,MAAM,gBAAgB,CAAC,sBAC5B,8OAAC;QAAe,SAAQ;QAAW,GAAG,KAAK;;;;;;AAGtC,MAAM,eAAe,CAAC,sBAC3B,8OAAC;QAAe,SAAQ;QAAU,GAAG,KAAK;;;;;;AAGrC,MAAM,gBAAgB,CAAC,sBAC5B,8OAAC;QAAe,SAAQ;QAAW,GAAG,KAAK;;;;;;AAGtC,MAAM,cAAc,CAAC,sBAC1B,8OAAC;QAAe,SAAQ;QAAS,GAAG,KAAK", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/simple-actions.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>, <PERSON>, Check<PERSON><PERSON>cle, Trash2, Alert<PERSON>riangle, X } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\ninterface SimpleActionsProps {\n  user: {\n    id_utente: number\n    ruolo: string\n    abilitato: boolean\n    username: string\n  }\n  onEdit: () => void\n  onToggleStatus: () => void\n  onDelete: () => void\n}\n\nexport default function SimpleActions({\n  user,\n  onEdit,\n  onToggleStatus,\n  onDelete\n}: SimpleActionsProps) {\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)\n\n  const handleEdit = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    onEdit()\n  }\n\n  const handleToggleStatus = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n\n    // Conferma per disabilitazione\n    if (user.abilitato) {\n      const confirmed = window.confirm(\n        `Sei sicuro di voler disabilitare l'utente \"${user.username}\"?\\n\\nL'utente non potrà più accedere al sistema.`\n      )\n      if (!confirmed) return\n    }\n\n    onToggleStatus()\n  }\n\n  const handleDeleteClick = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setShowDeleteConfirm(true)\n  }\n\n  const handleDeleteConfirm = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setShowDeleteConfirm(false)\n    onDelete()\n  }\n\n  const handleDeleteCancel = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setShowDeleteConfirm(false)\n  }\n\n  if (showDeleteConfirm) {\n    return (\n      <div className=\"flex items-center gap-1 bg-red-50 border border-red-200 rounded-md p-2\">\n        <div className=\"flex items-center gap-2\">\n          <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n          <span className=\"text-xs text-red-700 font-medium\">Eliminare?</span>\n        </div>\n        <div className=\"flex items-center gap-1 ml-2\">\n          <Button\n            size=\"sm\"\n            variant=\"destructive\"\n            onClick={handleDeleteConfirm}\n            className=\"h-6 px-2 text-xs\"\n          >\n            Sì\n          </Button>\n          <Button\n            size=\"sm\"\n            variant=\"outline\"\n            onClick={handleDeleteCancel}\n            className=\"h-6 px-2 text-xs\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center gap-1\">\n      {/* Modifica - sempre disponibile */}\n      <div className=\"relative group\">\n        <button\n          onClick={handleEdit}\n          type=\"button\"\n          className=\"p-1.5 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1\"\n          aria-label={`Modifica utente ${user.username}`}\n        >\n          <Edit className=\"h-4 w-4 text-blue-600 hover:text-blue-700\" />\n        </button>\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10\">\n          Modifica utente\n        </div>\n      </div>\n\n      {/* Abilita/Disabilita */}\n      <div className=\"relative group\">\n        <button\n          onClick={handleToggleStatus}\n          disabled={user.ruolo === 'owner'}\n          type=\"button\"\n          className={`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${\n            user.ruolo === 'owner'\n              ? 'opacity-50 cursor-not-allowed'\n              : 'hover:scale-105 hover:bg-slate-50 focus:ring-slate-500'\n          }`}\n          aria-label={user.abilitato ? `Disabilita utente ${user.username}` : `Abilita utente ${user.username}`}\n        >\n          {user.abilitato ? (\n            <Clock className=\"h-4 w-4 text-red-500 hover:text-red-600\" />\n          ) : (\n            <CheckCircle className=\"h-4 w-4 text-green-500 hover:text-green-600\" />\n          )}\n        </button>\n        {user.ruolo !== 'owner' && (\n          <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10\">\n            {user.abilitato ? 'Disabilita utente' : 'Abilita utente'}\n          </div>\n        )}\n      </div>\n\n      {/* Elimina */}\n      <div className=\"relative group\">\n        <button\n          onClick={handleDeleteClick}\n          disabled={user.ruolo === 'owner'}\n          type=\"button\"\n          className={`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${\n            user.ruolo === 'owner'\n              ? 'opacity-50 cursor-not-allowed'\n              : 'hover:scale-105 hover:bg-red-50 focus:ring-red-500'\n          }`}\n          aria-label={`Elimina utente ${user.username}`}\n        >\n          <Trash2 className=\"h-4 w-4 text-red-500 hover:text-red-600\" />\n        </button>\n        {user.ruolo !== 'owner' && (\n          <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10\">\n            Elimina utente\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAkBe,SAAS,cAAc,EACpC,IAAI,EACJ,MAAM,EACN,cAAc,EACd,QAAQ,EACW;IACnB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,+BAA+B;QAC/B,IAAI,KAAK,SAAS,EAAE;YAClB,MAAM,YAAY,OAAO,OAAO,CAC9B,CAAC,2CAA2C,EAAE,KAAK,QAAQ,CAAC,iDAAiD,CAAC;YAEhH,IAAI,CAAC,WAAW;QAClB;QAEA;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,qBAAqB;IACvB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,qBAAqB;QACrB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,qBAAqB;IACvB;IAEA,IAAI,mBAAmB;QACrB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;8BAErD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,MAAK;wBACL,WAAU;wBACV,cAAY,CAAC,gBAAgB,EAAE,KAAK,QAAQ,EAAE;kCAE9C,cAAA,8OAAC,2MAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;wBAAI,WAAU;kCAA+N;;;;;;;;;;;;0BAMhP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,UAAU,KAAK,KAAK,KAAK;wBACzB,MAAK;wBACL,WAAW,CAAC,iGAAiG,EAC3G,KAAK,KAAK,KAAK,UACX,kCACA,0DACJ;wBACF,cAAY,KAAK,SAAS,GAAG,CAAC,kBAAkB,EAAE,KAAK,QAAQ,EAAE,GAAG,CAAC,eAAe,EAAE,KAAK,QAAQ,EAAE;kCAEpG,KAAK,SAAS,iBACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;iDAEjB,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;oBAG1B,KAAK,KAAK,KAAK,yBACd,8OAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,GAAG,sBAAsB;;;;;;;;;;;;0BAM9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,UAAU,KAAK,KAAK,KAAK;wBACzB,MAAK;wBACL,WAAW,CAAC,iGAAiG,EAC3G,KAAK,KAAK,KAAK,UACX,kCACA,sDACJ;wBACF,cAAY,CAAC,eAAe,EAAE,KAAK,QAAQ,EAAE;kCAE7C,cAAA,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;oBAEnB,KAAK,KAAK,KAAK,yBACd,8OAAC;wBAAI,WAAU;kCAA+N;;;;;;;;;;;;;;;;;;AAOxP", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        data-slot=\"input\"\n        className={cn(\n          \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n          \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/softColors.ts"], "sourcesContent": ["/**\n * Palette di colori morbidi per il sistema CMS\n * Evita il rosso fuoco e usa tonalità più professionali\n */\n\nexport const SOFT_COLORS = {\n  // Stati di successo - Verde morbido\n  SUCCESS: {\n    bg: 'bg-emerald-50',\n    text: 'text-emerald-700',\n    border: 'border-emerald-200',\n    hover: 'hover:bg-emerald-100',\n    hex: '#10b981' // emerald-500\n  },\n\n  // Stati di warning - Giallo ambra/senape\n  WARNING: {\n    bg: 'bg-amber-50',\n    text: 'text-amber-700',\n    border: 'border-amber-200',\n    hover: 'hover:bg-amber-100',\n    hex: '#f59e0b' // amber-500\n  },\n\n  // Stati di attenzione - Arancione tenue\n  ATTENTION: {\n    bg: 'bg-orange-50',\n    text: 'text-orange-700',\n    border: 'border-orange-200',\n    hover: 'hover:bg-orange-100',\n    hex: '#ea580c' // orange-600\n  },\n\n  // Stati di errore - Rosso morbido (non fuoco)\n  ERROR: {\n    bg: 'bg-rose-50',\n    text: 'text-rose-700',\n    border: 'border-rose-200',\n    hover: 'hover:bg-rose-100',\n    hex: '#e11d48' // rose-600\n  },\n\n  // Stati informativi - Blu morbido\n  INFO: {\n    bg: 'bg-sky-50',\n    text: 'text-sky-700',\n    border: 'border-sky-200',\n    hover: 'hover:bg-sky-100',\n    hex: '#0284c7' // sky-600\n  },\n\n  // Stati neutri - Grigio\n  NEUTRAL: {\n    bg: 'bg-slate-50',\n    text: 'text-slate-700',\n    border: 'border-slate-200',\n    hover: 'hover:bg-slate-100',\n    hex: '#475569' // slate-600\n  },\n\n  // Stati di progresso - Indaco\n  PROGRESS: {\n    bg: 'bg-indigo-50',\n    text: 'text-indigo-700',\n    border: 'border-indigo-200',\n    hover: 'hover:bg-indigo-100',\n    hex: '#4f46e5' // indigo-600\n  }\n}\n\n/**\n * Colori specifici per stati bobine\n */\nexport const BOBINA_COLORS = {\n  DISPONIBILE: SOFT_COLORS.SUCCESS,\n  IN_USO: SOFT_COLORS.PROGRESS,\n  TERMINATA: SOFT_COLORS.NEUTRAL,\n  OVER: SOFT_COLORS.WARNING, // Giallo ambra invece di rosso\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati cavi\n */\nexport const CAVO_COLORS = {\n  DA_INSTALLARE: SOFT_COLORS.NEUTRAL,\n  INSTALLATO: SOFT_COLORS.SUCCESS,\n  COLLEGATO_PARTENZA: SOFT_COLORS.INFO,\n  COLLEGATO_ARRIVO: SOFT_COLORS.INFO,\n  COLLEGATO: SOFT_COLORS.PROGRESS,\n  CERTIFICATO: SOFT_COLORS.SUCCESS,\n  SPARE: SOFT_COLORS.WARNING,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati comande\n */\nexport const COMANDA_COLORS = {\n  ATTIVA: SOFT_COLORS.SUCCESS,\n  COMPLETATA: SOFT_COLORS.PROGRESS,\n  ANNULLATA: SOFT_COLORS.NEUTRAL,\n  IN_CORSO: SOFT_COLORS.INFO,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Funzioni helper per ottenere classi CSS\n */\nexport const getSoftColorClasses = (colorType: keyof typeof SOFT_COLORS) => {\n  const color = SOFT_COLORS[colorType]\n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getBobinaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS\n  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getCavoColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof CAVO_COLORS\n  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getComandaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof COMANDA_COLORS\n  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\n/**\n * Colori per percentuali di progresso\n */\nexport const getProgressColor = (percentage: number) => {\n  if (percentage >= 90) return SOFT_COLORS.SUCCESS\n  if (percentage >= 70) return SOFT_COLORS.PROGRESS\n  if (percentage >= 50) return SOFT_COLORS.INFO\n  if (percentage >= 30) return SOFT_COLORS.WARNING\n  return SOFT_COLORS.ATTENTION\n}\n\n/**\n * Colori per priorità\n */\nexport const PRIORITY_COLORS = {\n  ALTA: SOFT_COLORS.ERROR,\n  MEDIA: SOFT_COLORS.WARNING,\n  BASSA: SOFT_COLORS.INFO,\n  NORMALE: SOFT_COLORS.NEUTRAL\n}\n\nexport const getPriorityColorClasses = (priority: string) => {\n  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS\n  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAEM,MAAM,cAAc;IACzB,oCAAoC;IACpC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,cAAc;IAC/B;IAEA,yCAAyC;IACzC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,wCAAwC;IACxC,WAAW;QACT,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;IAEA,8CAA8C;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,WAAW;IAC5B;IAEA,kCAAkC;IAClC,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,UAAU;IAC3B;IAEA,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,8BAA8B;IAC9B,UAAU;QACR,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;AACF;AAKO,MAAM,gBAAgB;IAC3B,aAAa,YAAY,OAAO;IAChC,QAAQ,YAAY,QAAQ;IAC5B,WAAW,YAAY,OAAO;IAC9B,MAAM,YAAY,OAAO;IACzB,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,cAAc;IACzB,eAAe,YAAY,OAAO;IAClC,YAAY,YAAY,OAAO;IAC/B,oBAAoB,YAAY,IAAI;IACpC,kBAAkB,YAAY,IAAI;IAClC,WAAW,YAAY,QAAQ;IAC/B,aAAa,YAAY,OAAO;IAChC,OAAO,YAAY,OAAO;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,iBAAiB;IAC5B,QAAQ,YAAY,OAAO;IAC3B,YAAY,YAAY,QAAQ;IAChC,WAAW,YAAY,OAAO;IAC9B,UAAU,YAAY,IAAI;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,sBAAsB,CAAC;IAClC,MAAM,QAAQ,WAAW,CAAC,UAAU;IACpC,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,kBAAkB,OAAO;IAC/B,MAAM,QAAQ,aAAa,CAAC,gBAAgB,IAAI,cAAc,MAAM;IAEpE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,WAAW,CAAC,gBAAgB,IAAI,YAAY,MAAM;IAEhE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,cAAc,CAAC,gBAAgB,IAAI,eAAe,MAAM;IAEtE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,IAAI,cAAc,IAAI,OAAO,YAAY,QAAQ;IACjD,IAAI,cAAc,IAAI,OAAO,YAAY,IAAI;IAC7C,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,OAAO,YAAY,SAAS;AAC9B;AAKO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,KAAK;IACvB,OAAO,YAAY,OAAO;IAC1B,OAAO,YAAY,IAAI;IACvB,SAAS,YAAY,OAAO;AAC9B;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,qBAAqB,UAAU;IACrC,MAAM,QAAQ,eAAe,CAAC,mBAAmB,IAAI,gBAAgB,OAAO;IAE5E,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/securityValidation.ts"], "sourcesContent": ["/**\n * 🔒 SECURITY VALIDATION UTILITIES\n * Validazione robusta per prevenire attacchi di sicurezza\n */\n\n// Caratteri pericolosi da rimuovere/escape\nconst DANGEROUS_CHARS = /[<>\\\"'&\\x00-\\x1f\\x7f-\\x9f]/g\nconst SQL_INJECTION_PATTERNS = /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\\b)/gi\nconst XSS_PATTERNS = /(<script|javascript:|vbscript:|onload|onerror|onclick)/gi\n\n/**\n * Sanitizza input rimuovendo caratteri pericolosi\n */\nexport const sanitizeInput = (input: string): string => {\n  if (typeof input !== 'string') return ''\n  \n  return input\n    .trim()\n    .replace(DANGEROUS_CHARS, '') // Rimuove caratteri pericolosi\n    .replace(/\\s+/g, ' ') // Normalizza spazi\n    .substring(0, 1000) // <PERSON><PERSON> lunghezza massima\n}\n\n/**\n * Valida username con regole sicure\n */\nexport const validateUsername = (username: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(username)\n  \n  if (sanitized.length < 3) {\n    return { isValid: false, error: 'Username deve essere almeno 3 caratteri' }\n  }\n  \n  if (sanitized.length > 20) {\n    return { isValid: false, error: 'Username non può superare 20 caratteri' }\n  }\n  \n  if (!/^[a-zA-Z0-9._-]+$/.test(sanitized)) {\n    return { isValid: false, error: 'Username può contenere solo lettere, numeri, punti, underscore e trattini' }\n  }\n  \n  if (/^[._-]|[._-]$/.test(sanitized)) {\n    return { isValid: false, error: 'Username non può iniziare o finire con caratteri speciali' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida password con regole di sicurezza\n */\nexport const validatePassword = (password: string): { isValid: boolean; error?: string; strength: number } => {\n  if (!password || password.length < 8) {\n    return { isValid: false, error: 'Password deve essere almeno 8 caratteri', strength: 0 }\n  }\n  \n  if (password.length > 128) {\n    return { isValid: false, error: 'Password troppo lunga (max 128 caratteri)', strength: 0 }\n  }\n  \n  let strength = 0\n  \n  // Controlla criteri di sicurezza\n  if (/[a-z]/.test(password)) strength++\n  if (/[A-Z]/.test(password)) strength++\n  if (/[0-9]/.test(password)) strength++\n  if (/[^a-zA-Z0-9]/.test(password)) strength++\n  if (password.length >= 12) strength++\n  \n  if (strength < 3) {\n    return { \n      isValid: false, \n      error: 'Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale',\n      strength \n    }\n  }\n  \n  // Controlla password comuni\n  const commonPasswords = ['password', '123456', 'admin', 'qwerty', 'letmein']\n  if (commonPasswords.some(common => password.toLowerCase().includes(common))) {\n    return { isValid: false, error: 'Password troppo comune', strength }\n  }\n  \n  return { isValid: true, strength }\n}\n\n/**\n * Valida email con controlli di sicurezza\n */\nexport const validateEmail = (email: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(email)\n  \n  if (!sanitized) {\n    return { isValid: false, error: 'Email è obbligatoria' }\n  }\n  \n  if (sanitized.length > 254) {\n    return { isValid: false, error: 'Email troppo lunga' }\n  }\n  \n  // Regex RFC 5322 semplificata ma sicura\n  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/\n  \n  if (!emailRegex.test(sanitized)) {\n    return { isValid: false, error: 'Formato email non valido' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida testo generico con controlli XSS\n */\nexport const validateText = (text: string, maxLength: number = 255): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(text)\n  \n  if (sanitized.length > maxLength) {\n    return { isValid: false, error: `Testo troppo lungo (max ${maxLength} caratteri)` }\n  }\n  \n  // Controlla pattern XSS\n  if (XSS_PATTERNS.test(text)) {\n    return { isValid: false, error: 'Contenuto non consentito rilevato' }\n  }\n  \n  // Controlla pattern SQL injection\n  if (SQL_INJECTION_PATTERNS.test(text)) {\n    return { isValid: false, error: 'Contenuto non consentito rilevato' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida ragione sociale con controlli business\n */\nexport const validateRagioneSociale = (ragioneSociale: string): { isValid: boolean; error?: string } => {\n  const sanitized = sanitizeInput(ragioneSociale)\n  \n  if (!sanitized) {\n    return { isValid: false, error: 'Ragione sociale è obbligatoria' }\n  }\n  \n  if (sanitized.length < 2) {\n    return { isValid: false, error: 'Ragione sociale troppo corta' }\n  }\n  \n  if (sanitized.length > 100) {\n    return { isValid: false, error: 'Ragione sociale troppo lunga (max 100 caratteri)' }\n  }\n  \n  // Solo lettere, numeri, spazi e caratteri business comuni\n  if (!/^[a-zA-Z0-9\\s\\.\\-&']+$/.test(sanitized)) {\n    return { isValid: false, error: 'Ragione sociale contiene caratteri non consentiti' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Valida codice VAT/Partita IVA\n */\nexport const validateVAT = (vat: string): { isValid: boolean; error?: string } => {\n  if (!vat) return { isValid: true } // VAT è opzionale\n  \n  const sanitized = sanitizeInput(vat).replace(/\\s/g, '') // Rimuove spazi\n  \n  if (sanitized.length < 8 || sanitized.length > 15) {\n    return { isValid: false, error: 'VAT deve essere tra 8 e 15 caratteri' }\n  }\n  \n  // Solo numeri e lettere per VAT internazionali\n  if (!/^[A-Z0-9]+$/i.test(sanitized)) {\n    return { isValid: false, error: 'VAT può contenere solo lettere e numeri' }\n  }\n  \n  return { isValid: true }\n}\n\n/**\n * Rate limiting semplice (in-memory)\n */\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>()\n\nexport const checkRateLimit = (key: string, maxAttempts: number, windowMs: number): boolean => {\n  const now = Date.now()\n  const record = rateLimitStore.get(key)\n  \n  if (!record || now > record.resetTime) {\n    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })\n    return true\n  }\n  \n  if (record.count >= maxAttempts) {\n    return false\n  }\n  \n  record.count++\n  return true\n}\n\n/**\n * Genera token CSRF sicuro\n */\nexport const generateCSRFToken = (): string => {\n  const array = new Uint8Array(32)\n  crypto.getRandomValues(array)\n  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')\n}\n\n/**\n * Valida form completo utente\n */\nexport const validateUserForm = (formData: {\n  username: string\n  password?: string\n  ragione_sociale: string\n  email?: string\n  vat?: string\n  indirizzo?: string\n  nazione?: string\n  referente_aziendale?: string\n}): { isValid: boolean; errors: Record<string, string> } => {\n  const errors: Record<string, string> = {}\n  \n  // Valida username\n  const usernameValidation = validateUsername(formData.username)\n  if (!usernameValidation.isValid) {\n    errors.username = usernameValidation.error!\n  }\n  \n  // Valida password (se presente)\n  if (formData.password) {\n    const passwordValidation = validatePassword(formData.password)\n    if (!passwordValidation.isValid) {\n      errors.password = passwordValidation.error!\n    }\n  }\n  \n  // Valida ragione sociale\n  const ragioneSocialeValidation = validateRagioneSociale(formData.ragione_sociale)\n  if (!ragioneSocialeValidation.isValid) {\n    errors.ragione_sociale = ragioneSocialeValidation.error!\n  }\n  \n  // Valida email (se presente)\n  if (formData.email) {\n    const emailValidation = validateEmail(formData.email)\n    if (!emailValidation.isValid) {\n      errors.email = emailValidation.error!\n    }\n  }\n  \n  // Valida VAT (se presente)\n  if (formData.vat) {\n    const vatValidation = validateVAT(formData.vat)\n    if (!vatValidation.isValid) {\n      errors.vat = vatValidation.error!\n    }\n  }\n  \n  // Valida campi testo opzionali\n  if (formData.indirizzo) {\n    const indirizzoValidation = validateText(formData.indirizzo, 200)\n    if (!indirizzoValidation.isValid) {\n      errors.indirizzo = indirizzoValidation.error!\n    }\n  }\n  \n  if (formData.nazione) {\n    const nazioneValidation = validateText(formData.nazione, 50)\n    if (!nazioneValidation.isValid) {\n      errors.nazione = nazioneValidation.error!\n    }\n  }\n  \n  if (formData.referente_aziendale) {\n    const referenteValidation = validateText(formData.referente_aziendale, 100)\n    if (!referenteValidation.isValid) {\n      errors.referente_aziendale = referenteValidation.error!\n    }\n  }\n  \n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,2CAA2C;;;;;;;;;;;;;AAC3C,MAAM,kBAAkB;AACxB,MAAM,yBAAyB;AAC/B,MAAM,eAAe;AAKd,MAAM,gBAAgB,CAAC;IAC5B,IAAI,OAAO,UAAU,UAAU,OAAO;IAEtC,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,iBAAiB,IAAI,+BAA+B;KAC5D,OAAO,CAAC,QAAQ,KAAK,mBAAmB;KACxC,SAAS,CAAC,GAAG,MAAM,2BAA2B;;AACnD;AAKO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,YAAY,cAAc;IAEhC,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI,UAAU,MAAM,GAAG,IAAI;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyC;IAC3E;IAEA,IAAI,CAAC,oBAAoB,IAAI,CAAC,YAAY;QACxC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4E;IAC9G;IAEA,IAAI,gBAAgB,IAAI,CAAC,YAAY;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4D;IAC9F;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,YAAY,SAAS,MAAM,GAAG,GAAG;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO;YAA2C,UAAU;QAAE;IACzF;IAEA,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO;YAAE,SAAS;YAAO,OAAO;YAA6C,UAAU;QAAE;IAC3F;IAEA,IAAI,WAAW;IAEf,iCAAiC;IACjC,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,QAAQ,IAAI,CAAC,WAAW;IAC5B,IAAI,eAAe,IAAI,CAAC,WAAW;IACnC,IAAI,SAAS,MAAM,IAAI,IAAI;IAE3B,IAAI,WAAW,GAAG;QAChB,OAAO;YACL,SAAS;YACT,OAAO;YACP;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB;QAAC;QAAY;QAAU;QAAS;QAAU;KAAU;IAC5E,IAAI,gBAAgB,IAAI,CAAC,CAAA,SAAU,SAAS,WAAW,GAAG,QAAQ,CAAC,UAAU;QAC3E,OAAO;YAAE,SAAS;YAAO,OAAO;YAA0B;QAAS;IACrE;IAEA,OAAO;QAAE,SAAS;QAAM;IAAS;AACnC;AAKO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,YAAY,cAAc;IAEhC,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuB;IACzD;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,wCAAwC;IACxC,MAAM,aAAa;IAEnB,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO;QAA2B;IAC7D;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,eAAe,CAAC,MAAc,YAAoB,GAAG;IAChE,MAAM,YAAY,cAAc;IAEhC,IAAI,UAAU,MAAM,GAAG,WAAW;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,wBAAwB,EAAE,UAAU,WAAW,CAAC;QAAC;IACpF;IAEA,wBAAwB;IACxB,IAAI,aAAa,IAAI,CAAC,OAAO;QAC3B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,kCAAkC;IAClC,IAAI,uBAAuB,IAAI,CAAC,OAAO;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,cAAc;IAEhC,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAAiC;IACnE;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;IAEA,IAAI,UAAU,MAAM,GAAG,KAAK;QAC1B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAmD;IACrF;IAEA,0DAA0D;IAC1D,IAAI,CAAC,yBAAyB,IAAI,CAAC,YAAY;QAC7C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoD;IACtF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,KAAK,OAAO;QAAE,SAAS;IAAK,EAAE,kBAAkB;;IAErD,MAAM,YAAY,cAAc,KAAK,OAAO,CAAC,OAAO,IAAI,gBAAgB;;IAExE,IAAI,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,IAAI;QACjD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuC;IACzE;IAEA,+CAA+C;IAC/C,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY;QACnC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA;;CAEC,GACD,MAAM,iBAAiB,IAAI;AAEpB,MAAM,iBAAiB,CAAC,KAAa,aAAqB;IAC/D,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,SAAS,eAAe,GAAG,CAAC;IAElC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;QACrC,eAAe,GAAG,CAAC,KAAK;YAAE,OAAO;YAAG,WAAW,MAAM;QAAS;QAC9D,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,IAAI,aAAa;QAC/B,OAAO;IACT;IAEA,OAAO,KAAK;IACZ,OAAO;AACT;AAKO,MAAM,oBAAoB;IAC/B,MAAM,QAAQ,IAAI,WAAW;IAC7B,OAAO,eAAe,CAAC;IACvB,OAAO,MAAM,IAAI,CAAC,OAAO,CAAA,OAAQ,KAAK,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAC5E;AAKO,MAAM,mBAAmB,CAAC;IAU/B,MAAM,SAAiC,CAAC;IAExC,kBAAkB;IAClB,MAAM,qBAAqB,iBAAiB,SAAS,QAAQ;IAC7D,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAC/B,OAAO,QAAQ,GAAG,mBAAmB,KAAK;IAC5C;IAEA,gCAAgC;IAChC,IAAI,SAAS,QAAQ,EAAE;QACrB,MAAM,qBAAqB,iBAAiB,SAAS,QAAQ;QAC7D,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC/B,OAAO,QAAQ,GAAG,mBAAmB,KAAK;QAC5C;IACF;IAEA,yBAAyB;IACzB,MAAM,2BAA2B,uBAAuB,SAAS,eAAe;IAChF,IAAI,CAAC,yBAAyB,OAAO,EAAE;QACrC,OAAO,eAAe,GAAG,yBAAyB,KAAK;IACzD;IAEA,6BAA6B;IAC7B,IAAI,SAAS,KAAK,EAAE;QAClB,MAAM,kBAAkB,cAAc,SAAS,KAAK;QACpD,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,OAAO,KAAK,GAAG,gBAAgB,KAAK;QACtC;IACF;IAEA,2BAA2B;IAC3B,IAAI,SAAS,GAAG,EAAE;QAChB,MAAM,gBAAgB,YAAY,SAAS,GAAG;QAC9C,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,OAAO,GAAG,GAAG,cAAc,KAAK;QAClC;IACF;IAEA,+BAA+B;IAC/B,IAAI,SAAS,SAAS,EAAE;QACtB,MAAM,sBAAsB,aAAa,SAAS,SAAS,EAAE;QAC7D,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC,OAAO,SAAS,GAAG,oBAAoB,KAAK;QAC9C;IACF;IAEA,IAAI,SAAS,OAAO,EAAE;QACpB,MAAM,oBAAoB,aAAa,SAAS,OAAO,EAAE;QACzD,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC9B,OAAO,OAAO,GAAG,kBAAkB,KAAK;QAC1C;IACF;IAEA,IAAI,SAAS,mBAAmB,EAAE;QAChC,MAAM,sBAAsB,aAAa,SAAS,mBAAmB,EAAE;QACvE,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC,OAAO,mBAAmB,GAAG,oBAAoB,KAAK;QACxD;IACF;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/UserForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton, SecondaryButton } from '@/components/ui/animated-button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Badge } from '@/components/ui/badge'\nimport { usersApi } from '@/lib/api'\nimport { User } from '@/types'\nimport { validateUserForm, checkRateLimit } from '@/utils/securityValidation'\nimport { Eye, EyeOff, Loader2, Save, X, CheckCircle, AlertCircle, Info, Shield, Building2, Mail, MapPin, User as UserIcon, Setting<PERSON>, Clock, Edit } from 'lucide-react'\n\ninterface UserFormProps {\n  user?: User | null\n  onSave: (user: User) => void\n  onCancel: () => void\n}\n\nexport default function UserForm({ user, onSave, onCancel }: UserFormProps) {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    ruolo: 'user', // L'amministratore può creare solo utenti standard\n    data_scadenza: '',\n    abilitato: true,\n    // Nuovi campi aziendali\n    ragione_sociale: '',\n    indirizzo: '',\n    nazione: '',\n    email: '',\n    vat: '',\n    referente_aziendale: ''\n  })\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [passwordStrength, setPasswordStrength] = useState(0)\n  const [validationStatus, setValidationStatus] = useState<Record<string, 'valid' | 'invalid' | 'pending'>>({})\n  const [isFormValid, setIsFormValid] = useState(false)\n\n  // Calcola la forza della password\n  const calculatePasswordStrength = (password: string) => {\n    let strength = 0\n    if (password.length >= 8) strength += 25\n    if (/[a-z]/.test(password)) strength += 25\n    if (/[A-Z]/.test(password)) strength += 25\n    if (/[0-9]/.test(password)) strength += 25\n    if (/[^A-Za-z0-9]/.test(password)) strength += 25\n    return Math.min(strength, 100)\n  }\n\n  // Validazione in tempo reale\n  const validateField = (name: string, value: any) => {\n    let status: 'valid' | 'invalid' | 'pending' = 'pending'\n\n    switch (name) {\n      case 'username':\n        status = value && value.length >= 3 ? 'valid' : 'invalid'\n        break\n      case 'password':\n        if (!user) { // Password obbligatoria solo per nuovi utenti\n          status = value && value.length >= 8 ? 'valid' : 'invalid'\n        } else {\n          status = !value || value.length >= 8 ? 'valid' : 'invalid'\n        }\n        break\n      case 'ragione_sociale':\n        status = value && value.length >= 2 ? 'valid' : 'invalid'\n        break\n      case 'email':\n        if (value) {\n          const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n          status = emailRegex.test(value) ? 'valid' : 'invalid'\n        } else {\n          status = 'valid' // Email opzionale\n        }\n        break\n      default:\n        status = 'valid'\n    }\n\n    setValidationStatus(prev => ({ ...prev, [name]: status }))\n    return status\n  }\n\n  // Inizializza il form con i dati dell'utente se presente\n  useEffect(() => {\n    if (user) {\n      const initialData = {\n        username: user.username || '',\n        password: '', // Non mostrare la password esistente\n        ruolo: user.ruolo || 'user',\n        data_scadenza: user.data_scadenza ? user.data_scadenza.split('T')[0] : '',\n        abilitato: user.abilitato !== undefined ? user.abilitato : true,\n        // Nuovi campi aziendali\n        ragione_sociale: user.ragione_sociale || '',\n        indirizzo: user.indirizzo || '',\n        nazione: user.nazione || '',\n        email: user.email || '',\n        vat: user.vat || '',\n        referente_aziendale: user.referente_aziendale || ''\n      }\n      setFormData(initialData)\n\n      // Valida i campi iniziali\n      Object.entries(initialData).forEach(([key, value]) => {\n        validateField(key, value)\n      })\n    }\n  }, [user])\n\n  // Controlla se il form è valido\n  useEffect(() => {\n    const requiredFields = user ? ['username', 'ragione_sociale'] : ['username', 'password', 'ragione_sociale']\n    const isValid = requiredFields.every(field => validationStatus[field] === 'valid')\n    setIsFormValid(isValid)\n  }, [validationStatus, user])\n\n  // Gestisce il cambio dei valori del form\n  const handleChange = (name: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n\n    // Validazione in tempo reale\n    validateField(name, value)\n\n    // Calcola forza password se è il campo password\n    if (name === 'password') {\n      setPasswordStrength(calculatePasswordStrength(value))\n    }\n\n    // Rimuovi l'errore per questo campo se presente\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }))\n    }\n\n    // Rimuovi messaggi di successo/errore quando l'utente modifica il form\n    if (success) setSuccess('')\n    if (error) setError('')\n  }\n\n  // Validazione del form con controlli di sicurezza\n  const validateForm = () => {\n    // Validazione sicura completa\n    const validation = validateUserForm({\n      username: formData.username,\n      password: user ? undefined : formData.password, // Password obbligatoria solo per nuovi utenti\n      ragione_sociale: formData.ragione_sociale,\n      email: formData.email,\n      vat: formData.vat,\n      indirizzo: formData.indirizzo,\n      nazione: formData.nazione,\n      referente_aziendale: formData.referente_aziendale\n    })\n\n    setErrors(validation.errors)\n    return validation.isValid\n  }\n\n  // Gestisce il submit del form con protezione rate limiting\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    // Rate limiting per prevenire spam/brute force\n    const clientId = `user-form-${user?.id_utente || 'new'}-${Date.now()}`\n    if (!checkRateLimit(clientId, 5, 60000)) { // Max 5 tentativi per minuto\n      setError('Troppi tentativi. Riprova tra un minuto.')\n      return\n    }\n\n    if (!validateForm()) {\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      // Prepara i dati da inviare\n      const userData = {\n        ...formData\n      }\n\n      // Per nuovi utenti, forza sempre il ruolo \"user\"\n      if (!user) {\n        userData.ruolo = 'user'\n      }\n\n      // Rimuovi la password se è vuota (modifica utente)\n      if (user && !userData.password.trim()) {\n        delete (userData as any).password\n      }\n\n      // Converti la data in formato ISO se presente\n      if (userData.data_scadenza) {\n        userData.data_scadenza = userData.data_scadenza\n      }\n\n      let result\n      if (user) {\n        // Aggiorna l'utente esistente\n        result = await usersApi.updateUser(user.id_utente, userData)\n      } else {\n        // Crea un nuovo utente\n        result = await usersApi.createUser(userData)\n      }\n\n      setSuccess(user ? 'Utente aggiornato con successo!' : 'Nuovo utente creato con successo!')\n\n      // Attendi un momento per mostrare il messaggio di successo\n      setTimeout(() => {\n        onSave(result)\n      }, 1500)\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Errore durante il salvataggio dell\\'utente')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Componente per indicatore di validazione\n  const ValidationIndicator = ({ status }: { status: 'valid' | 'invalid' | 'pending' }) => {\n    if (status === 'valid') {\n      return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    } else if (status === 'invalid') {\n      return <AlertCircle className=\"h-4 w-4 text-red-500\" />\n    }\n    return null\n  }\n\n  // Componente per indicatore forza password\n  const PasswordStrengthIndicator = () => {\n    if (!formData.password) return null\n\n    const getStrengthColor = () => {\n      if (passwordStrength < 50) return 'bg-red-500'\n      if (passwordStrength < 75) return 'bg-yellow-500'\n      return 'bg-green-500'\n    }\n\n    const getStrengthText = () => {\n      if (passwordStrength < 25) return 'Molto debole'\n      if (passwordStrength < 50) return 'Debole'\n      if (passwordStrength < 75) return 'Media'\n      if (passwordStrength < 100) return 'Forte'\n      return 'Molto forte'\n    }\n\n    return (\n      <div className=\"mt-2 space-y-1\">\n        <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-slate-600\">Forza password:</span>\n          <span className={`font-medium ${passwordStrength < 50 ? 'text-red-600' : passwordStrength < 75 ? 'text-yellow-600' : 'text-green-600'}`}>\n            {getStrengthText()}\n          </span>\n        </div>\n        <div className=\"w-full bg-slate-200 rounded-full h-2\">\n          <div\n            className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor()}`}\n            style={{ width: `${passwordStrength}%` }}\n          />\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <Card className=\"shadow-lg\">\n      <CardHeader className=\"bg-gradient-to-r from-blue-50 to-indigo-50 border-b\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 bg-blue-100 rounded-lg\">\n            {user ? <Edit className=\"h-5 w-5 text-blue-600\" /> : <UserIcon className=\"h-5 w-5 text-blue-600\" />}\n          </div>\n          <div>\n            <CardTitle className=\"text-xl\">\n              {user ? `Modifica Utente: ${user.username}` : 'Crea Nuovo Utente Standard'}\n            </CardTitle>\n            <CardDescription>\n              {user ? 'Aggiorna le informazioni dell\\'utente esistente' : 'Inserisci i dati per creare un nuovo utente nel sistema'}\n            </CardDescription>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"p-6\">\n        {/* Messaggi di stato */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2\">\n            <AlertCircle className=\"h-5 w-5 text-red-600 flex-shrink-0\" />\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        )}\n\n        {success && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2\">\n            <CheckCircle className=\"h-5 w-5 text-green-600 flex-shrink-0\" />\n            <p className=\"text-green-600\">{success}</p>\n          </div>\n        )}\n\n        {/* Indicatore di progresso form */}\n        <div className=\"mb-6\">\n          <div className=\"flex items-center justify-between text-sm text-slate-600 mb-2\">\n            <span>Completamento form</span>\n            <span>{isFormValid ? '✓ Completo' : 'In corso...'}</span>\n          </div>\n          <div className=\"w-full bg-slate-200 rounded-full h-2\">\n            <div\n              className={`h-2 rounded-full transition-all duration-500 ${isFormValid ? 'bg-green-500' : 'bg-blue-500'}`}\n              style={{ width: `${isFormValid ? 100 : 60}%` }}\n            />\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* Sezione Credenziali */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 pb-2 border-b border-slate-200\">\n              <Shield className=\"h-5 w-5 text-blue-600\" />\n              <h3 className=\"text-lg font-semibold text-slate-900\">Credenziali di Accesso</h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Username */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"username\" className=\"flex items-center gap-2\">\n                  Username *\n                  <ValidationIndicator status={validationStatus.username || 'pending'} />\n                </Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"username\"\n                    value={formData.username}\n                    onChange={(e) => handleChange('username', e.target.value)}\n                    disabled={loading}\n                    className={`${errors.username ? 'border-red-500' : validationStatus.username === 'valid' ? 'border-green-500' : ''} transition-colors duration-200`}\n                    placeholder=\"Inserisci username univoco\"\n                  />\n                </div>\n                {errors.username && <p className=\"text-sm text-red-600 flex items-center gap-1\"><AlertCircle className=\"h-3 w-3\" />{errors.username}</p>}\n                {validationStatus.username === 'valid' && !errors.username && (\n                  <p className=\"text-sm text-green-600 flex items-center gap-1\"><CheckCircle className=\"h-3 w-3\" />Username valido</p>\n                )}\n              </div>\n\n              {/* Password */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"password\" className=\"flex items-center gap-2\">\n                  {user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password *'}\n                  <ValidationIndicator status={validationStatus.password || 'pending'} />\n                </Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                    value={formData.password}\n                    onChange={(e) => handleChange('password', e.target.value)}\n                    disabled={loading}\n                    className={`${errors.password ? 'border-red-500' : validationStatus.password === 'valid' ? 'border-green-500' : ''} pr-10 transition-colors duration-200`}\n                    placeholder={user ? \"Lascia vuoto per mantenere la password attuale\" : \"Inserisci password sicura\"}\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    disabled={loading}\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-4 w-4 text-gray-400\" />\n                    ) : (\n                      <Eye className=\"h-4 w-4 text-gray-400\" />\n                    )}\n                  </Button>\n                </div>\n                {errors.password && <p className=\"text-sm text-red-600 flex items-center gap-1\"><AlertCircle className=\"h-3 w-3\" />{errors.password}</p>}\n                <PasswordStrengthIndicator />\n              </div>\n            </div>\n          </div>\n\n          {/* Sezione Dati Aziendali */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 pb-2 border-b border-slate-200\">\n              <Building2 className=\"h-5 w-5 text-blue-600\" />\n              <h3 className=\"text-lg font-semibold text-slate-900\">Informazioni Aziendali</h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Ragione Sociale */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ragione_sociale\" className=\"flex items-center gap-2\">\n                  Ragione Sociale *\n                  <ValidationIndicator status={validationStatus.ragione_sociale || 'pending'} />\n                </Label>\n                <Input\n                  id=\"ragione_sociale\"\n                  value={formData.ragione_sociale}\n                  onChange={(e) => handleChange('ragione_sociale', e.target.value)}\n                  disabled={loading}\n                  className={`${errors.ragione_sociale ? 'border-red-500' : validationStatus.ragione_sociale === 'valid' ? 'border-green-500' : ''} transition-colors duration-200`}\n                  placeholder=\"Nome dell'azienda o organizzazione\"\n                />\n                {errors.ragione_sociale && <p className=\"text-sm text-red-600 flex items-center gap-1\"><AlertCircle className=\"h-3 w-3\" />{errors.ragione_sociale}</p>}\n              </div>\n\n              {/* Email */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"email\" className=\"flex items-center gap-2\">\n                  <Mail className=\"h-4 w-4\" />\n                  Email\n                  <ValidationIndicator status={validationStatus.email || 'pending'} />\n                </Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={(e) => handleChange('email', e.target.value)}\n                  disabled={loading}\n                  className={`${errors.email ? 'border-red-500' : validationStatus.email === 'valid' ? 'border-green-500' : ''} transition-colors duration-200`}\n                  placeholder=\"<EMAIL>\"\n                />\n                {errors.email && <p className=\"text-sm text-red-600 flex items-center gap-1\"><AlertCircle className=\"h-3 w-3\" />{errors.email}</p>}\n              </div>\n\n              {/* Indirizzo */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"indirizzo\" className=\"flex items-center gap-2\">\n                  <MapPin className=\"h-4 w-4\" />\n                  Indirizzo\n                </Label>\n                <Input\n                  id=\"indirizzo\"\n                  value={formData.indirizzo}\n                  onChange={(e) => handleChange('indirizzo', e.target.value)}\n                  disabled={loading}\n                  placeholder=\"Via, numero civico, città\"\n                  className=\"transition-colors duration-200\"\n                />\n              </div>\n\n              {/* Nazione */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"nazione\">Nazione</Label>\n                <Input\n                  id=\"nazione\"\n                  value={formData.nazione}\n                  onChange={(e) => handleChange('nazione', e.target.value)}\n                  disabled={loading}\n                  placeholder=\"Italia\"\n                  className=\"transition-colors duration-200\"\n                />\n              </div>\n\n              {/* VAT */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"vat\">Partita IVA</Label>\n                <Input\n                  id=\"vat\"\n                  value={formData.vat}\n                  onChange={(e) => handleChange('vat', e.target.value)}\n                  disabled={loading}\n                  placeholder=\"*************\"\n                  className=\"transition-colors duration-200\"\n                />\n              </div>\n\n              {/* Referente Aziendale */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"referente_aziendale\">Referente Aziendale</Label>\n                <Input\n                  id=\"referente_aziendale\"\n                  value={formData.referente_aziendale}\n                  onChange={(e) => handleChange('referente_aziendale', e.target.value)}\n                  disabled={loading}\n                  placeholder=\"Nome e cognome del referente\"\n                  className=\"transition-colors duration-200\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Sezione Configurazioni */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 pb-2 border-b border-slate-200\">\n              <Settings className=\"h-5 w-5 text-blue-600\" />\n              <h3 className=\"text-lg font-semibold text-slate-900\">Configurazioni Account</h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Data Scadenza */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"data_scadenza\" className=\"flex items-center gap-2\">\n                  <Clock className=\"h-4 w-4\" />\n                  Data Scadenza\n                </Label>\n                <Input\n                  id=\"data_scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => handleChange('data_scadenza', e.target.value)}\n                  disabled={loading}\n                  className=\"transition-colors duration-200\"\n                />\n                <p className=\"text-xs text-slate-500\">Lascia vuoto per account senza scadenza</p>\n              </div>\n\n              {/* Ruolo - Solo per modifica, per creazione è sempre \"user\" */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ruolo\">Ruolo Utente</Label>\n                {user ? (\n                  <Select\n                    value={formData.ruolo}\n                    onValueChange={(value) => handleChange('ruolo', value)}\n                    disabled={loading}\n                  >\n                    <SelectTrigger className=\"transition-colors duration-200\">\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"user\">User Standard</SelectItem>\n                      <SelectItem value=\"cantieri_user\">Cantieri User</SelectItem>\n                    </SelectContent>\n                  </Select>\n                ) : (\n                  <div className=\"px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2\">\n                    <Badge variant=\"outline\" className=\"bg-blue-100 text-blue-700\">User Standard</Badge>\n                    <span>Ruolo predefinito per nuovi utenti</span>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Utente Abilitato */}\n            <div className=\"flex items-center space-x-3 p-4 bg-slate-50 rounded-lg\">\n              <Checkbox\n                id=\"abilitato\"\n                checked={formData.abilitato}\n                onCheckedChange={(checked) => handleChange('abilitato', checked)}\n                disabled={loading || (user && user.ruolo === 'owner')}\n              />\n              <div className=\"flex-1\">\n                <Label htmlFor=\"abilitato\" className=\"font-medium\">Account Abilitato</Label>\n                <p className=\"text-sm text-slate-600\">L'utente può accedere al sistema e utilizzare le funzionalità</p>\n              </div>\n              {formData.abilitato ? (\n                <Badge className=\"bg-green-100 text-green-700\">Attivo</Badge>\n              ) : (\n                <Badge variant=\"outline\" className=\"bg-red-100 text-red-700\">Disabilitato</Badge>\n              )}\n            </div>\n          </div>\n\n          {/* Pulsanti */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-slate-200\">\n            <div className=\"text-sm text-slate-600\">\n              {isFormValid ? (\n                <span className=\"flex items-center gap-1 text-green-600\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  Form completato correttamente\n                </span>\n              ) : (\n                <span className=\"flex items-center gap-1\">\n                  <Info className=\"h-4 w-4\" />\n                  Completa i campi obbligatori per continuare\n                </span>\n              )}\n            </div>\n\n            <div className=\"flex space-x-4\">\n              <SecondaryButton\n                type=\"button\"\n                onClick={onCancel}\n                disabled={loading}\n                icon={<X className=\"h-4 w-4\" />}\n                className=\"min-w-[120px]\"\n              >\n                Annulla\n              </SecondaryButton>\n              <PrimaryButton\n                type=\"submit\"\n                loading={loading}\n                disabled={!isFormValid}\n                icon={<Save className=\"h-4 w-4\" />}\n                glow={isFormValid}\n                className=\"min-w-[120px]\"\n              >\n                {loading ? 'Salvataggio...' : user ? 'Aggiorna Utente' : 'Crea Utente'}\n              </PrimaryButton>\n            </div>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;;;;;AAsBe,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,WAAW;QACX,wBAAwB;QACxB,iBAAiB;QACjB,WAAW;QACX,SAAS;QACT,OAAO;QACP,KAAK;QACL,qBAAqB;IACvB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD,CAAC;IAC3G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kCAAkC;IAClC,MAAM,4BAA4B,CAAC;QACjC,IAAI,WAAW;QACf,IAAI,SAAS,MAAM,IAAI,GAAG,YAAY;QACtC,IAAI,QAAQ,IAAI,CAAC,WAAW,YAAY;QACxC,IAAI,QAAQ,IAAI,CAAC,WAAW,YAAY;QACxC,IAAI,QAAQ,IAAI,CAAC,WAAW,YAAY;QACxC,IAAI,eAAe,IAAI,CAAC,WAAW,YAAY;QAC/C,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC,MAAc;QACnC,IAAI,SAA0C;QAE9C,OAAQ;YACN,KAAK;gBACH,SAAS,SAAS,MAAM,MAAM,IAAI,IAAI,UAAU;gBAChD;YACF,KAAK;gBACH,IAAI,CAAC,MAAM;oBACT,SAAS,SAAS,MAAM,MAAM,IAAI,IAAI,UAAU;gBAClD,OAAO;oBACL,SAAS,CAAC,SAAS,MAAM,MAAM,IAAI,IAAI,UAAU;gBACnD;gBACA;YACF,KAAK;gBACH,SAAS,SAAS,MAAM,MAAM,IAAI,IAAI,UAAU;gBAChD;YACF,KAAK;gBACH,IAAI,OAAO;oBACT,MAAM,aAAa;oBACnB,SAAS,WAAW,IAAI,CAAC,SAAS,UAAU;gBAC9C,OAAO;oBACL,SAAS,QAAQ,kBAAkB;;gBACrC;gBACA;YACF;gBACE,SAAS;QACb;QAEA,oBAAoB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAO,CAAC;QACxD,OAAO;IACT;IAEA,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,MAAM,cAAc;gBAClB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,UAAU;gBACV,OAAO,KAAK,KAAK,IAAI;gBACrB,eAAe,KAAK,aAAa,GAAG,KAAK,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBACvE,WAAW,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG;gBAC3D,wBAAwB;gBACxB,iBAAiB,KAAK,eAAe,IAAI;gBACzC,WAAW,KAAK,SAAS,IAAI;gBAC7B,SAAS,KAAK,OAAO,IAAI;gBACzB,OAAO,KAAK,KAAK,IAAI;gBACrB,KAAK,KAAK,GAAG,IAAI;gBACjB,qBAAqB,KAAK,mBAAmB,IAAI;YACnD;YACA,YAAY;YAEZ,0BAA0B;YAC1B,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC/C,cAAc,KAAK;YACrB;QACF;IACF,GAAG;QAAC;KAAK;IAET,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,OAAO;YAAC;YAAY;SAAkB,GAAG;YAAC;YAAY;YAAY;SAAkB;QAC3G,MAAM,UAAU,eAAe,KAAK,CAAC,CAAA,QAAS,gBAAgB,CAAC,MAAM,KAAK;QAC1E,eAAe;IACjB,GAAG;QAAC;QAAkB;KAAK;IAE3B,yCAAyC;IACzC,MAAM,eAAe,CAAC,MAAc;QAClC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,6BAA6B;QAC7B,cAAc,MAAM;QAEpB,gDAAgD;QAChD,IAAI,SAAS,YAAY;YACvB,oBAAoB,0BAA0B;QAChD;QAEA,gDAAgD;QAChD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;QAEA,uEAAuE;QACvE,IAAI,SAAS,WAAW;QACxB,IAAI,OAAO,SAAS;IACtB;IAEA,kDAAkD;IAClD,MAAM,eAAe;QACnB,8BAA8B;QAC9B,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE;YAClC,UAAU,SAAS,QAAQ;YAC3B,UAAU,OAAO,YAAY,SAAS,QAAQ;YAC9C,iBAAiB,SAAS,eAAe;YACzC,OAAO,SAAS,KAAK;YACrB,KAAK,SAAS,GAAG;YACjB,WAAW,SAAS,SAAS;YAC7B,SAAS,SAAS,OAAO;YACzB,qBAAqB,SAAS,mBAAmB;QACnD;QAEA,UAAU,WAAW,MAAM;QAC3B,OAAO,WAAW,OAAO;IAC3B;IAEA,2DAA2D;IAC3D,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,+CAA+C;QAC/C,MAAM,WAAW,CAAC,UAAU,EAAE,MAAM,aAAa,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI;QACtE,IAAI,CAAC,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,GAAG,QAAQ;YACvC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,4BAA4B;YAC5B,MAAM,WAAW;gBACf,GAAG,QAAQ;YACb;YAEA,iDAAiD;YACjD,IAAI,CAAC,MAAM;gBACT,SAAS,KAAK,GAAG;YACnB;YAEA,mDAAmD;YACnD,IAAI,QAAQ,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;gBACrC,OAAO,AAAC,SAAiB,QAAQ;YACnC;YAEA,8CAA8C;YAC9C,IAAI,SAAS,aAAa,EAAE;gBAC1B,SAAS,aAAa,GAAG,SAAS,aAAa;YACjD;YAEA,IAAI;YACJ,IAAI,MAAM;gBACR,8BAA8B;gBAC9B,SAAS,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;YACrD,OAAO;gBACL,uBAAuB;gBACvB,SAAS,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;YACrC;YAEA,WAAW,OAAO,oCAAoC;YAEtD,2DAA2D;YAC3D,WAAW;gBACT,OAAO;YACT,GAAG;QACL,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,2CAA2C;IAC3C,MAAM,sBAAsB,CAAC,EAAE,MAAM,EAA+C;QAClF,IAAI,WAAW,SAAS;YACtB,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC,OAAO,IAAI,WAAW,WAAW;YAC/B,qBAAO,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,OAAO;IACT;IAEA,2CAA2C;IAC3C,MAAM,4BAA4B;QAChC,IAAI,CAAC,SAAS,QAAQ,EAAE,OAAO;QAE/B,MAAM,mBAAmB;YACvB,IAAI,mBAAmB,IAAI,OAAO;YAClC,IAAI,mBAAmB,IAAI,OAAO;YAClC,OAAO;QACT;QAEA,MAAM,kBAAkB;YACtB,IAAI,mBAAmB,IAAI,OAAO;YAClC,IAAI,mBAAmB,IAAI,OAAO;YAClC,IAAI,mBAAmB,IAAI,OAAO;YAClC,IAAI,mBAAmB,KAAK,OAAO;YACnC,OAAO;QACT;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;sCACjC,8OAAC;4BAAK,WAAW,CAAC,YAAY,EAAE,mBAAmB,KAAK,iBAAiB,mBAAmB,KAAK,oBAAoB,kBAAkB;sCACpI;;;;;;;;;;;;8BAGL,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAW,CAAC,6CAA6C,EAAE,oBAAoB;wBAC/E,OAAO;4BAAE,OAAO,GAAG,iBAAiB,CAAC,CAAC;wBAAC;;;;;;;;;;;;;;;;;IAKjD;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,qBAAO,8OAAC,2MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;qDAA6B,8OAAC,kMAAA,CAAA,OAAQ;gCAAC,WAAU;;;;;;;;;;;sCAE3E,8OAAC;;8CACC,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAClB,OAAO,CAAC,iBAAiB,EAAE,KAAK,QAAQ,EAAE,GAAG;;;;;;8CAEhD,8OAAC,gIAAA,CAAA,kBAAe;8CACb,OAAO,oDAAoD;;;;;;;;;;;;;;;;;;;;;;;0BAKpE,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAIhC,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAKnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAM,cAAc,eAAe;;;;;;;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,6CAA6C,EAAE,cAAc,iBAAiB,eAAe;oCACzG,OAAO;wCAAE,OAAO,GAAG,cAAc,MAAM,GAAG,CAAC,CAAC;oCAAC;;;;;;;;;;;;;;;;;kCAKnD,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;;4DAA0B;0EAE5D,8OAAC;gEAAoB,QAAQ,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;kEAE5D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;4DACxD,UAAU;4DACV,WAAW,GAAG,OAAO,QAAQ,GAAG,mBAAmB,iBAAiB,QAAQ,KAAK,UAAU,qBAAqB,GAAG,+BAA+B,CAAC;4DACnJ,aAAY;;;;;;;;;;;oDAGf,OAAO,QAAQ,kBAAI,8OAAC;wDAAE,WAAU;;0EAA+C,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAa,OAAO,QAAQ;;;;;;;oDAClI,iBAAiB,QAAQ,KAAK,WAAW,CAAC,OAAO,QAAQ,kBACxD,8OAAC;wDAAE,WAAU;;0EAAiD,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;0DAKrG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;;4DACjC,OAAO,qDAAqD;0EAC7D,8OAAC;gEAAoB,QAAQ,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;kEAE5D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAM,eAAe,SAAS;gEAC9B,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;gEACxD,UAAU;gEACV,WAAW,GAAG,OAAO,QAAQ,GAAG,mBAAmB,iBAAiB,QAAQ,KAAK,UAAU,qBAAqB,GAAG,qCAAqC,CAAC;gEACzJ,aAAa,OAAO,mDAAmD;;;;;;0EAEzE,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,gBAAgB,CAAC;gEAChC,UAAU;0EAET,6BACC,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;yFAElB,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;;;;;;;oDAIpB,OAAO,QAAQ,kBAAI,8OAAC;wDAAE,WAAU;;0EAA+C,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAa,OAAO,QAAQ;;;;;;;kEACnI,8OAAC;;;;;;;;;;;;;;;;;;;;;;;0CAMP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAkB,WAAU;;4DAA0B;0EAEnE,8OAAC;gEAAoB,QAAQ,iBAAiB,eAAe,IAAI;;;;;;;;;;;;kEAEnE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,eAAe;wDAC/B,UAAU,CAAC,IAAM,aAAa,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAC/D,UAAU;wDACV,WAAW,GAAG,OAAO,eAAe,GAAG,mBAAmB,iBAAiB,eAAe,KAAK,UAAU,qBAAqB,GAAG,+BAA+B,CAAC;wDACjK,aAAY;;;;;;oDAEb,OAAO,eAAe,kBAAI,8OAAC;wDAAE,WAAU;;0EAA+C,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAa,OAAO,eAAe;;;;;;;;;;;;;0DAInJ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAQ,WAAU;;0EAC/B,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;0EAE5B,8OAAC;gEAAoB,QAAQ,iBAAiB,KAAK,IAAI;;;;;;;;;;;;kEAEzD,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;wDACrD,UAAU;wDACV,WAAW,GAAG,OAAO,KAAK,GAAG,mBAAmB,iBAAiB,KAAK,KAAK,UAAU,qBAAqB,GAAG,+BAA+B,CAAC;wDAC7I,aAAY;;;;;;oDAEb,OAAO,KAAK,kBAAI,8OAAC;wDAAE,WAAU;;0EAA+C,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAa,OAAO,KAAK;;;;;;;;;;;;;0DAI/H,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;0EACnC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGhC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,SAAS;wDACzB,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;wDACzD,UAAU;wDACV,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;wDACvD,UAAU;wDACV,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAM;;;;;;kEACrB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,GAAG;wDACnB,UAAU,CAAC,IAAM,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;wDACnD,UAAU;wDACV,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAsB;;;;;;kEACrC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,mBAAmB;wDACnC,UAAU,CAAC,IAAM,aAAa,uBAAuB,EAAE,MAAM,CAAC,KAAK;wDACnE,UAAU;wDACV,aAAY;wDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAOlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAgB,WAAU;;0EACvC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG/B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,aAAa;wDAC7B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAC7D,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;oDACtB,qBACC,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,SAAS,KAAK;wDACrB,eAAe,CAAC,QAAU,aAAa,SAAS;wDAChD,UAAU;;0EAEV,8OAAC,kIAAA,CAAA,gBAAa;gEAAC,WAAU;0EACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;kFACzB,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAgB;;;;;;;;;;;;;;;;;6EAItC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAA4B;;;;;;0EAC/D,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAOd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS,SAAS,SAAS;gDAC3B,iBAAiB,CAAC,UAAY,aAAa,aAAa;gDACxD,UAAU,WAAY,QAAQ,KAAK,KAAK,KAAK;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAAc;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;4CAEvC,SAAS,SAAS,iBACjB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAA8B;;;;;qEAE/C,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAMnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,4BACC,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAY;;;;;;iEAIrC,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAMlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8IAAA,CAAA,kBAAe;gDACd,MAAK;gDACL,SAAS;gDACT,UAAU;gDACV,oBAAM,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDACnB,WAAU;0DACX;;;;;;0DAGD,8OAAC,8IAAA,CAAA,gBAAa;gDACZ,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC;gDACX,oBAAM,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACtB,MAAM;gDACN,WAAU;0DAET,UAAU,mBAAmB,OAAO,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE", "debugId": null}}, {"offset": {"line": 2875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/DatabaseView.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton } from '@/components/ui/animated-button'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\n\nimport { usersApi } from '@/lib/api'\nimport { Loader2, Database, RefreshCw, Eye } from 'lucide-react'\n\nexport default function DatabaseView() {\n  const [dbData, setDbData] = useState<any>(null)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  // Carica tutti i dati del database\n  const loadDatabaseData = async () => {\n    setLoading(true)\n    setError('')\n\n    try {\n      const data = await usersApi.getDatabaseData()\n      setDbData(data)\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Errore durante il caricamento dei dati del database')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    loadDatabaseData()\n  }, [])\n\n  // Renderizza una tabella specifica\n  const renderTable = (tableName: string, tableData: any[], title: string) => {\n    if (!tableData || tableData.length === 0) {\n      return (\n        <div className=\"text-center py-4 text-slate-500 border rounded-lg\">\n          Nessun dato disponibile per {title}\n        </div>\n      )\n    }\n\n    // Ottieni le chiavi per le colonne (dal primo elemento)\n    const columns = Object.keys(tableData[0])\n\n    return (\n      <div className=\"border rounded-lg overflow-hidden mb-6\">\n        <div className=\"bg-slate-100 px-4 py-3 border-b\">\n          <h4 className=\"font-medium text-slate-900\">{title}</h4>\n          <p className=\"text-sm text-slate-600\">Totale record: {tableData.length}</p>\n        </div>\n        <div className=\"overflow-x-auto max-h-96\">\n          <Table>\n            <TableHeader className=\"sticky top-0 bg-slate-50\">\n              <TableRow>\n                {columns.map((column) => (\n                  <TableHead key={column} className=\"font-medium\">\n                    {column}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {tableData.map((row, index) => (\n                <TableRow key={index}>\n                  {columns.map((column) => (\n                    <TableCell key={column} className=\"font-mono text-sm\">\n                      {row[column] !== null && row[column] !== undefined\n                        ? String(row[column])\n                        : <span className=\"text-slate-400\">NULL</span>\n                      }\n                    </TableCell>\n                  ))}\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </div>\n      </div>\n    )\n  }\n\n  // Definisce le tabelle disponibili\n  const tables = [\n    { key: 'users', title: 'Utenti', description: 'Tutti gli utenti del sistema' },\n    { key: 'cantieri', title: 'Cantieri', description: 'Tutti i cantieri/progetti' },\n    { key: 'cavi', title: 'Cavi', description: 'Tutti i cavi installati' },\n    { key: 'parco_cavi', title: 'Bobine', description: 'Tutte le bobine del parco cavi' },\n    { key: 'strumenti_certificati', title: 'Strumenti', description: 'Strumenti certificati' },\n    { key: 'certificazioni_cavi', title: 'Certificazioni', description: 'Certificazioni dei cavi' }\n  ]\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <Database className=\"h-5 w-5\" />\n            Visualizzazione Database Raw\n          </CardTitle>\n          <PrimaryButton\n            size=\"sm\"\n            onClick={loadDatabaseData}\n            loading={loading}\n            icon={<RefreshCw className=\"h-4 w-4\" />}\n          >\n            Aggiorna\n          </PrimaryButton>\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <Eye className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-blue-900\">Visualizzazione Raw del Database</h4>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                Questa sezione mostra i dati grezzi delle tabelle del database. \n                Utile per debugging e analisi dei dati.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <Loader2 className=\"h-8 w-8 animate-spin mr-3\" />\n            <span className=\"text-lg\">Caricamento dati database...</span>\n          </div>\n        ) : error ? (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\n            <p className=\"text-red-600 font-medium\">Errore durante il caricamento:</p>\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        ) : !dbData ? (\n          <div className=\"text-center py-12 text-slate-500\">\n            Nessun dato disponibile\n          </div>\n        ) : (\n          <div className=\"space-y-8\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <p className=\"text-sm text-blue-700\">\n                Visualizzazione completa di tutte le tabelle del database.\n                I dati sono mostrati in formato raw per debugging e analisi.\n              </p>\n            </div>\n\n            {tables.map((table) => (\n              dbData[table.key] && (\n                <div key={table.key}>\n                  <div className=\"mb-4\">\n                    <h3 className=\"text-xl font-semibold text-slate-900\">{table.title}</h3>\n                    <p className=\"text-sm text-slate-600\">{table.description}</p>\n                  </div>\n                  {renderTable(table.key, dbData[table.key], table.title)}\n                </div>\n              )\n            ))}\n\n            {/* Mostra un riepilogo */}\n            <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-slate-900 mb-2\">Riepilogo Database</h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm\">\n                {tables.map((table) => (\n                  <div key={table.key} className=\"flex justify-between\">\n                    <span className=\"text-slate-600\">{table.title}:</span>\n                    <span className=\"font-medium\">\n                      {dbData[table.key] ? dbData[table.key].length : 0} record\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AATA;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,mCAAmC;IACnC,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,OAAO,MAAM,iHAAA,CAAA,WAAQ,CAAC,eAAe;YAC3C,UAAU;QACZ,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,cAAc,CAAC,WAAmB,WAAkB;QACxD,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;YACxC,qBACE,8OAAC;gBAAI,WAAU;;oBAAoD;oBACpC;;;;;;;QAGnC;QAEA,wDAAwD;QACxD,MAAM,UAAU,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE;QAExC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAE,WAAU;;gCAAyB;gCAAgB,UAAU,MAAM;;;;;;;;;;;;;8BAExE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC,iIAAA,CAAA,WAAQ;8CACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4CAAc,WAAU;sDAC/B;2CADa;;;;;;;;;;;;;;;0CAMtB,8OAAC,iIAAA,CAAA,YAAS;0CACP,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,iIAAA,CAAA,WAAQ;kDACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;gDAAc,WAAU;0DAC/B,GAAG,CAAC,OAAO,KAAK,QAAQ,GAAG,CAAC,OAAO,KAAK,YACrC,OAAO,GAAG,CAAC,OAAO,kBAClB,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;+CAHvB;;;;;uCAFL;;;;;;;;;;;;;;;;;;;;;;;;;;;IAgB7B;IAEA,mCAAmC;IACnC,MAAM,SAAS;QACb;YAAE,KAAK;YAAS,OAAO;YAAU,aAAa;QAA+B;QAC7E;YAAE,KAAK;YAAY,OAAO;YAAY,aAAa;QAA4B;QAC/E;YAAE,KAAK;YAAQ,OAAO;YAAQ,aAAa;QAA0B;QACrE;YAAE,KAAK;YAAc,OAAO;YAAU,aAAa;QAAiC;QACpF;YAAE,KAAK;YAAyB,OAAO;YAAa,aAAa;QAAwB;QACzF;YAAE,KAAK;YAAuB,OAAO;YAAkB,aAAa;QAA0B;KAC/F;IAED,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,8IAAA,CAAA,gBAAa;4BACZ,MAAK;4BACL,SAAS;4BACT,SAAS;4BACT,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;sCAC5B;;;;;;;;;;;;;;;;;0BAKL,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;oBAQ/C,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;+BAE1B,sBACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,CAAC,uBACH,8OAAC;wBAAI,WAAU;kCAAmC;;;;;6CAIlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;4BAMtC,OAAO,GAAG,CAAC,CAAC,QACX,MAAM,CAAC,MAAM,GAAG,CAAC,kBACf,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC,MAAM,KAAK;;;;;;8DACjE,8OAAC;oDAAE,WAAU;8DAA0B,MAAM,WAAW;;;;;;;;;;;;wCAEzD,YAAY,MAAM,GAAG,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,KAAK;;mCAL9C,MAAM,GAAG;;;;;0CAWvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAK,WAAU;;4DAAkB,MAAM,KAAK;4DAAC;;;;;;;kEAC9C,8OAAC;wDAAK,WAAU;;4DACb,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG;4DAAE;;;;;;;;+CAH5C,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrC", "debugId": null}}, {"offset": {"line": 3354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/ResetDatabase.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { DangerButton, SecondaryButton } from '@/components/ui/animated-button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { usersApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Loader2, RotateCcw, AlertTriangle, Trash2, Shield, Clock, Eye, EyeOff } from 'lucide-react'\n\nexport default function ResetDatabase() {\n  const { user } = useAuth()\n  const [confirmText, setConfirmText] = useState('')\n  const [confirmChecked, setConfirmChecked] = useState(false)\n  const [adminPassword, setAdminPassword] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState('')\n  const [countdown, setCountdown] = useState(10)\n  const [countdownActive, setCountdownActive] = useState(false)\n  const [finalConfirmation, setFinalConfirmation] = useState(false)\n\n  // Timer di countdown per la conferma finale\n  useEffect(() => {\n    let interval: NodeJS.Timeout\n    if (countdownActive && countdown > 0) {\n      interval = setInterval(() => {\n        setCountdown(prev => prev - 1)\n      }, 1000)\n    } else if (countdown === 0) {\n      setFinalConfirmation(true)\n      setCountdownActive(false)\n    }\n    return () => clearInterval(interval)\n  }, [countdownActive, countdown])\n\n  const startCountdown = () => {\n    if (confirmText === 'RESET DATABASE' && confirmChecked && adminPassword.trim()) {\n      setCountdownActive(true)\n      setCountdown(10)\n      setFinalConfirmation(false)\n      setError('')\n    } else {\n      setError('Completa tutti i passaggi di conferma prima di procedere')\n    }\n  }\n\n  const handleReset = async () => {\n    if (!finalConfirmation) {\n      setError('Devi completare il countdown di sicurezza')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n    setSuccess('')\n\n    try {\n      // Verifica password admin (simulata - in produzione verificare con il backend)\n      if (!adminPassword.trim()) {\n        throw new Error('Password amministratore richiesta')\n      }\n\n      await usersApi.resetDatabase()\n      setSuccess('Database resettato con successo! Tutti i dati sono stati eliminati.')\n\n      // Reset form\n      setConfirmText('')\n      setConfirmChecked(false)\n      setAdminPassword('')\n      setFinalConfirmation(false)\n      setCountdownActive(false)\n      setCountdown(10)\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Errore durante il reset del database')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const isPreResetEnabled = confirmText === 'RESET DATABASE' && confirmChecked && adminPassword.trim() && !loading && !countdownActive\n  const isResetEnabled = finalConfirmation && !loading\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2 text-red-600\">\n          <RotateCcw className=\"h-5 w-5\" />\n          Reset Database\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Avviso di pericolo */}\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <AlertTriangle className=\"h-6 w-6 text-red-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-bold text-red-900 text-lg\">⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE</h4>\n              <div className=\"text-red-700 mt-2 space-y-2\">\n                <p className=\"font-medium\">\n                  Questa operazione eliminerà PERMANENTEMENTE tutti i dati dal database:\n                </p>\n                <ul className=\"list-disc list-inside space-y-1 text-sm\">\n                  <li>Tutti gli utenti (eccetto l'amministratore principale)</li>\n                  <li>Tutti i cantieri e i progetti</li>\n                  <li>Tutti i cavi installati</li>\n                  <li>Tutte le bobine del parco cavi</li>\n                  <li>Tutti i comandi e le certificazioni</li>\n                  <li>Tutti i report e i dati di produttività</li>\n                </ul>\n                <p className=\"font-bold text-red-800 mt-3\">\n                  NON È POSSIBILE RECUPERARE I DATI DOPO IL RESET!\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Messaggi di stato */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n            <p className=\"text-red-600\">{error}</p>\n          </div>\n        )}\n\n        {success && (\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n            <p className=\"text-green-600\">{success}</p>\n          </div>\n        )}\n\n        {/* Form di conferma */}\n        <div className=\"space-y-4 border-t pt-6\">\n          <div>\n            <h4 className=\"font-semibold text-slate-900 mb-4\">\n              Conferma Reset Database\n            </h4>\n            <p className=\"text-sm text-slate-600 mb-4\">\n              Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:\n            </p>\n          </div>\n\n          <div className=\"space-y-6\">\n            {/* Step 1: Digitare testo di conferma */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"confirm-text\" className=\"text-sm font-medium\">\n                1. Digita esattamente: <code className=\"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold\">RESET DATABASE</code>\n              </Label>\n              <Input\n                id=\"confirm-text\"\n                value={confirmText}\n                onChange={(e) => setConfirmText(e.target.value)}\n                placeholder=\"Digita: RESET DATABASE\"\n                disabled={loading || countdownActive}\n                className={confirmText === 'RESET DATABASE' ? 'border-green-500' : ''}\n              />\n            </div>\n\n            {/* Step 2: Checkbox di conferma */}\n            <div className=\"flex items-start space-x-3\">\n              <Checkbox\n                id=\"confirm-checkbox\"\n                checked={confirmChecked}\n                onCheckedChange={setConfirmChecked}\n                disabled={loading || countdownActive}\n              />\n              <Label htmlFor=\"confirm-checkbox\" className=\"text-sm leading-relaxed\">\n                2. Confermo di aver compreso che questa operazione eliminerà TUTTI i dati dal database\n                in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario.\n              </Label>\n            </div>\n\n            {/* Step 3: Password amministratore */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"admin-password\" className=\"text-sm font-medium flex items-center gap-2\">\n                <Shield className=\"h-4 w-4 text-blue-600\" />\n                3. Inserisci la tua password di amministratore per confermare l'identità\n              </Label>\n              <div className=\"relative\">\n                <Input\n                  id=\"admin-password\"\n                  type={showPassword ? 'text' : 'password'}\n                  value={adminPassword}\n                  onChange={(e) => setAdminPassword(e.target.value)}\n                  placeholder=\"Password amministratore\"\n                  disabled={loading || countdownActive}\n                  className={adminPassword.trim() ? 'border-green-500' : ''}\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  disabled={loading || countdownActive}\n                >\n                  {showPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Stato di conferma */}\n          <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n            <h5 className=\"font-medium text-slate-900 mb-3\">Stato Conferma:</h5>\n            <div className=\"space-y-2 text-sm\">\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  confirmText === 'RESET DATABASE' ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <span>Testo di conferma: {confirmText === 'RESET DATABASE' ? '✓ Corretto' : '✗ Richiesto'}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  confirmChecked ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <span>Checkbox confermata: {confirmChecked ? '✓ Sì' : '✗ Richiesta'}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-3 h-3 rounded-full ${\n                  adminPassword.trim() ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n                <span>Password amministratore: {adminPassword.trim() ? '✓ Inserita' : '✗ Richiesta'}</span>\n              </div>\n              {countdownActive && (\n                <div className=\"flex items-center gap-2 mt-3 p-2 bg-orange-50 border border-orange-200 rounded\">\n                  <Clock className=\"h-4 w-4 text-orange-600\" />\n                  <span className=\"text-orange-700 font-medium\">\n                    Countdown di sicurezza: {countdown} secondi\n                  </span>\n                </div>\n              )}\n              {finalConfirmation && (\n                <div className=\"flex items-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded\">\n                  <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n                  <span className=\"text-red-700 font-medium\">\n                    ⚠️ Pronto per il reset - Ultima possibilità di annullare\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Pulsanti di azione */}\n          <div className=\"space-y-3\">\n            {!finalConfirmation && !countdownActive && (\n              <DangerButton\n                onClick={startCountdown}\n                disabled={!isPreResetEnabled}\n                className=\"w-full\"\n                size=\"lg\"\n                icon={<Clock className=\"h-5 w-5\" />}\n              >\n                INIZIA COUNTDOWN DI SICUREZZA (10 secondi)\n              </DangerButton>\n            )}\n\n            {countdownActive && (\n              <div className=\"w-full p-4 bg-orange-50 border-2 border-orange-300 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center gap-3\">\n                  <Clock className=\"h-6 w-6 text-orange-600 animate-pulse\" />\n                  <span className=\"text-lg font-bold text-orange-700\">\n                    Countdown: {countdown} secondi\n                  </span>\n                </div>\n                <p className=\"text-sm text-orange-600 mt-2\">\n                  Il pulsante di reset si attiverà al termine del countdown\n                </p>\n              </div>\n            )}\n\n            {finalConfirmation && (\n              <DangerButton\n                onClick={handleReset}\n                disabled={!isResetEnabled}\n                className=\"w-full animate-pulse\"\n                size=\"lg\"\n                loading={loading}\n                icon={<Trash2 className=\"h-5 w-5\" />}\n                glow\n              >\n                {loading ? 'RESET IN CORSO...' : '🚨 RESET DATABASE - ELIMINA TUTTI I DATI 🚨'}\n              </DangerButton>\n            )}\n\n            {!isPreResetEnabled && !finalConfirmation && !countdownActive && (\n              <p className=\"text-center text-sm text-slate-500\">\n                Completa tutti i passaggi di conferma per iniziare il countdown\n              </p>\n            )}\n\n            {finalConfirmation && (\n              <SecondaryButton\n                onClick={() => {\n                  setFinalConfirmation(false)\n                  setCountdownActive(false)\n                  setCountdown(10)\n                }}\n                className=\"w-full\"\n                size=\"lg\"\n                disabled={loading}\n              >\n                ANNULLA RESET\n              </SecondaryButton>\n            )}\n          </div>\n        </div>\n\n        {/* Informazioni aggiuntive */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm\">\n          <h5 className=\"font-medium text-blue-900 mb-2\">Informazioni Tecniche:</h5>\n          <ul className=\"text-blue-700 space-y-1\">\n            <li>• Il reset manterrà la struttura delle tabelle</li>\n            <li>• L'utente amministratore principale verrà ricreato</li>\n            <li>• Le configurazioni di sistema verranno ripristinate ai valori di default</li>\n            <li>• L'operazione può richiedere alcuni minuti per completarsi</li>\n          </ul>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,mBAAmB,YAAY,GAAG;YACpC,WAAW,YAAY;gBACrB,aAAa,CAAA,OAAQ,OAAO;YAC9B,GAAG;QACL,OAAO,IAAI,cAAc,GAAG;YAC1B,qBAAqB;YACrB,mBAAmB;QACrB;QACA,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,iBAAiB;QACrB,IAAI,gBAAgB,oBAAoB,kBAAkB,cAAc,IAAI,IAAI;YAC9E,mBAAmB;YACnB,aAAa;YACb,qBAAqB;YACrB,SAAS;QACX,OAAO;YACL,SAAS;QACX;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,mBAAmB;YACtB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,+EAA+E;YAC/E,IAAI,CAAC,cAAc,IAAI,IAAI;gBACzB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,iHAAA,CAAA,WAAQ,CAAC,aAAa;YAC5B,WAAW;YAEX,aAAa;YACb,eAAe;YACf,kBAAkB;YAClB,iBAAiB;YACjB,qBAAqB;YACrB,mBAAmB;YACnB,aAAa;QACf,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,UAAU,IAAI,OAAO,IAAI;QACxD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,gBAAgB,oBAAoB,kBAAkB,cAAc,IAAI,MAAM,CAAC,WAAW,CAAC;IACrH,MAAM,iBAAiB,qBAAqB,CAAC;IAE7C,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIrC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAG3B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASlD,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;oBAIhC,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAkB;;;;;;;;;;;kCAKnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;;;;;;;0CAK7C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;;oDAAsB;kEACrC,8OAAC;wDAAK,WAAU;kEAAwD;;;;;;;;;;;;0DAEjG,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,aAAY;gDACZ,UAAU,WAAW;gDACrB,WAAW,gBAAgB,mBAAmB,qBAAqB;;;;;;;;;;;;kDAKvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS;gDACT,iBAAiB;gDACjB,UAAU,WAAW;;;;;;0DAEvB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAmB,WAAU;0DAA0B;;;;;;;;;;;;kDAOxE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAiB,WAAU;;kEACxC,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA0B;;;;;;;0DAG9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAM,eAAe,SAAS;wDAC9B,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAChD,aAAY;wDACZ,UAAU,WAAW;wDACrB,WAAW,cAAc,IAAI,KAAK,qBAAqB;;;;;;kEAEzD,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,gBAAgB,CAAC;wDAChC,UAAU,WAAW;kEAEpB,6BAAe,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAAe,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,gBAAgB,mBAAmB,iBAAiB,cACpD;;;;;;kEACF,8OAAC;;4DAAK;4DAAoB,gBAAgB,mBAAmB,eAAe;;;;;;;;;;;;;0DAE9E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,iBAAiB,iBAAiB,cAClC;;;;;;kEACF,8OAAC;;4DAAK;4DAAsB,iBAAiB,SAAS;;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,cAAc,IAAI,KAAK,iBAAiB,cACxC;;;;;;kEACF,8OAAC;;4DAAK;4DAA0B,cAAc,IAAI,KAAK,eAAe;;;;;;;;;;;;;4CAEvE,iCACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;;4DAA8B;4DACnB;4DAAU;;;;;;;;;;;;;4CAIxC,mCACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;;;;;;;;;;;;;;0CASnD,8OAAC;gCAAI,WAAU;;oCACZ,CAAC,qBAAqB,CAAC,iCACtB,8OAAC,8IAAA,CAAA,eAAY;wCACX,SAAS;wCACT,UAAU,CAAC;wCACX,WAAU;wCACV,MAAK;wCACL,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;kDACxB;;;;;;oCAKF,iCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;;4DAAoC;4DACtC;4DAAU;;;;;;;;;;;;;0DAG1B,8OAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;oCAM/C,mCACC,8OAAC,8IAAA,CAAA,eAAY;wCACX,SAAS;wCACT,UAAU,CAAC;wCACX,WAAU;wCACV,MAAK;wCACL,SAAS;wCACT,oBAAM,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACxB,IAAI;kDAEH,UAAU,sBAAsB;;;;;;oCAIpC,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,iCAC5C,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;oCAKnD,mCACC,8OAAC,8IAAA,CAAA,kBAAe;wCACd,SAAS;4CACP,qBAAqB;4CACrB,mBAAmB;4CACnB,aAAa;wCACf;wCACA,WAAU;wCACV,MAAK;wCACL,UAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 4155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/admin/TipologieCaviManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Cable, Building, FileText, Tag, Plus } from 'lucide-react'\n\nexport default function TipologieCaviManager() {\n  const [activeTab, setActiveTab] = useState('categorie')\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Cable className=\"h-5 w-5\" />\n          Database Tipologie Cavi\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start gap-3\">\n            <Cable className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n            <div>\n              <h4 className=\"font-medium text-blue-900\">Database Enciclopedico Tipologie Cavi</h4>\n              <p className=\"text-sm text-blue-700 mt-1\">\n                Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, \n                standard e tipologie specifiche. Questo database serve come riferimento per \n                la classificazione e gestione dei cavi nei progetti.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"categorie\" className=\"flex items-center gap-2\">\n              <Tag className=\"h-4 w-4\" />\n              Categorie\n            </TabsTrigger>\n            <TabsTrigger value=\"produttori\" className=\"flex items-center gap-2\">\n              <Building className=\"h-4 w-4\" />\n              Produttori\n            </TabsTrigger>\n            <TabsTrigger value=\"standard\" className=\"flex items-center gap-2\">\n              <FileText className=\"h-4 w-4\" />\n              Standard\n            </TabsTrigger>\n            <TabsTrigger value=\"tipologie\" className=\"flex items-center gap-2\">\n              <Cable className=\"h-4 w-4\" />\n              Tipologie\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"categorie\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Categorie Cavi</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuova Categoria\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Tag className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione categorie cavi - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile creare, modificare ed eliminare le categorie di cavi\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"produttori\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Produttori</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuovo Produttore\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Building className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione produttori - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire l'anagrafica dei produttori di cavi\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"standard\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Standard e Normative</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuovo Standard\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <FileText className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione standard - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire gli standard tecnici e le normative di riferimento\n              </p>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"tipologie\" className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"text-lg font-semibold\">Tipologie Specifiche</h3>\n                <p className=\"text-sm text-slate-600\">\n                  Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche\n                </p>\n              </div>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Nuova Tipologia\n              </Button>\n            </div>\n            \n            <div className=\"text-center py-12 text-slate-500\">\n              <Cable className=\"h-12 w-12 mx-auto mb-4 text-slate-400\" />\n              <p>Gestione tipologie - Da implementare</p>\n              <p className=\"text-sm mt-2\">\n                Qui sarà possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate\n              </p>\n            </div>\n          </TabsContent>\n        </Tabs>\n\n        {/* Informazioni aggiuntive */}\n        <div className=\"bg-slate-50 border border-slate-200 rounded-lg p-4\">\n          <h5 className=\"font-medium text-slate-900 mb-2\">Struttura Database Tipologie:</h5>\n          <div className=\"text-sm text-slate-600 space-y-1\">\n            <p><strong>Categorie:</strong> Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)</p>\n            <p><strong>Produttori:</strong> Aziende produttrici con informazioni di contatto</p>\n            <p><strong>Standard:</strong> Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)</p>\n            <p><strong>Tipologie:</strong> Specifiche tecniche dettagliate per ogni tipo di cavo</p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIjC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAShD,8OAAC,gIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAa,WAAU;;0DACxC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKjC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;;kDACxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;0CAMhC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAmB;;;;;;;kDAC9B,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAoB;;;;;;;kDAC/B,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAkB;;;;;;;kDAC7B,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 4813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport '@/styles/admin.css'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { AnimatedButton, PrimaryButton, SecondaryButton, DangerButton, OutlineButton, QuickButton } from '@/components/ui/animated-button'\nimport CompactActionsDropdown from '@/components/ui/compact-actions-dropdown'\nimport SimpleActions from '@/components/ui/simple-actions'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { getSoftColorClasses } from '@/utils/softColors'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { api, cantieriApi, usersApi } from '@/lib/api'\nimport { User, Cantiere } from '@/types'\nimport UserForm from '@/components/admin/UserForm'\nimport DatabaseView from '@/components/admin/DatabaseView'\nimport ResetDatabase from '@/components/admin/ResetDatabase'\nimport TipologieCaviManager from '@/components/admin/TipologieCaviManager'\nimport {\n  Settings,\n  Users,\n  Building2,\n  Search,\n  Plus,\n  Edit,\n  Trash2,\n  CheckCircle,\n  Clock,\n  AlertCircle,\n  Eye,\n  Shield,\n  Key,\n  Loader2,\n  UserPlus,\n  LogIn,\n  Cable,\n  Database,\n  RotateCcw,\n  RefreshCw\n} from 'lucide-react'\n\nexport default function AdminPage() {\n  const router = useRouter()\n  const [activeTab, setActiveTab] = useState('visualizza-utenti')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [users, setUsers] = useState<User[]>([])\n  const [cantieri, setCantieri] = useState<Cantiere[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedUser, setSelectedUser] = useState<User | null>(null)\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' })\n\n  const { user, impersonateUser } = useAuth()\n\n  // Carica dati dal backend\n  useEffect(() => {\n    loadData()\n  }, [activeTab])\n\n  const loadData = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      if (activeTab === 'visualizza-utenti' || activeTab === 'crea-utente' || activeTab === 'accedi-come-utente') {\n        const usersData = await usersApi.getUsers()\n        setUsers(usersData)\n      } else if (activeTab === 'cantieri') {\n        const cantieriData = await cantieriApi.getCantieri()\n        setCantieri(cantieriData)\n      }\n    } catch (error: any) {\n      setError(error.response?.data?.detail || error.message || 'Errore durante il caricamento dei dati')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleEditUser = (userToEdit: User) => {\n    setSelectedUser(userToEdit)\n    setActiveTab('modifica-utente')\n  }\n\n  const handleToggleUserStatus = async (userId: number) => {\n    try {\n      await usersApi.toggleUserStatus(userId)\n      loadData() // Ricarica i dati\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante la modifica dello stato utente')\n    }\n  }\n\n  const handleDeleteUser = async (userId: number) => {\n    if (confirm('Sei sicuro di voler eliminare questo utente?')) {\n      try {\n        await usersApi.deleteUser(userId)\n        loadData() // Ricarica i dati\n      } catch (error: any) {\n        setError(error.response?.data?.detail || 'Errore durante l\\'eliminazione dell\\'utente')\n      }\n    }\n  }\n\n  const handleSaveUser = (savedUser: User) => {\n    setSelectedUser(null)\n    setActiveTab('visualizza-utenti')\n    loadData() // Ricarica i dati\n  }\n\n  const handleCancelForm = () => {\n    setSelectedUser(null)\n    setActiveTab('visualizza-utenti')\n  }\n\n  const handleQuickImpersonate = async (targetUser: User) => {\n    try {\n\n      await impersonateUser(targetUser.id_utente)\n\n      // Reindirizza in base al ruolo dell'utente impersonato\n      if (targetUser.ruolo === 'user') {\n        router.push('/cantieri')\n      } else if (targetUser.ruolo === 'cantieri_user') {\n        router.push('/cavi')\n      } else {\n        router.push('/')\n      }\n    } catch (error: any) {\n      setError(error.response?.data?.detail || error.message || 'Errore durante l\\'impersonificazione')\n    }\n  }\n\n  // Helper functions per i badge\n\n  const getRuoloBadge = (ruolo: string) => {\n    let colorType: keyof typeof getSoftColorClasses = 'NEUTRAL'\n\n    switch (ruolo) {\n      case 'owner':\n        colorType = 'PROGRESS'\n        break\n      case 'user':\n        colorType = 'INFO'\n        break\n      case 'cantieri_user':\n        colorType = 'SUCCESS'\n        break\n      default:\n        colorType = 'NEUTRAL'\n        break\n    }\n\n    const colorClasses = getSoftColorClasses(colorType)\n    return <Badge className={colorClasses.badge}>{ruolo}</Badge>\n  }\n\n  const getStatusBadge = (abilitato: boolean, data_scadenza?: string) => {\n    let colorType: keyof typeof getSoftColorClasses = 'SUCCESS'\n    let label = 'Attivo'\n    let icon = '●'\n\n    if (!abilitato) {\n      colorType = 'ERROR'\n      label = 'Disabilitato'\n      icon = '●'\n    } else if (data_scadenza) {\n      const scadenza = new Date(data_scadenza)\n      const oggi = new Date()\n\n      if (scadenza < oggi) {\n        colorType = 'ERROR'\n        label = 'Scaduto'\n        icon = '⚠'\n      } else if (scadenza.getTime() - oggi.getTime() < 7 * 24 * 60 * 60 * 1000) {\n        colorType = 'WARNING'\n        label = 'In Scadenza'\n        icon = '⏰'\n      } else {\n        icon = '✓'\n      }\n    } else {\n      icon = '✓'\n    }\n\n    const colorClasses = getSoftColorClasses(colorType)\n    return (\n      <Badge className={`${colorClasses.badge} flex items-center gap-1`}>\n        <span className=\"text-xs\" role=\"img\" aria-hidden=\"true\">{icon}</span>\n        <span>{label}</span>\n      </Badge>\n    )\n  }\n\n  const filteredUsers = users.filter(u =>\n    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    u.ragione_sociale?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    u.email?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  // Verifica se l'utente ha permessi di amministrazione\n  // Durante il logout, user diventa null, quindi reindirizza direttamente al login\n  if (!user) {\n    if (typeof window !== 'undefined') {\n      window.location.replace('/login')\n    }\n    return null\n  }\n\n  if (user.ruolo !== 'owner') {\n    if (typeof window !== 'undefined') {\n      window.location.replace('/login')\n    }\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n\n\n\n        {/* Tabs - Design migliorato con separazione visiva per azioni pericolose */}\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <div className=\"space-y-4\">\n            {/* Tab principali */}\n            <div className=\"bg-white rounded-lg border border-slate-200 shadow-sm p-1\">\n              <TabsList className={`grid w-full ${selectedUser ? 'grid-cols-5' : 'grid-cols-4'} gap-1 h-auto bg-transparent p-0`}>\n                <TabsTrigger\n                  value=\"visualizza-utenti\"\n                  className=\"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent\"\n                >\n                  <Users className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">Gestione Utenti</span>\n                </TabsTrigger>\n                <TabsTrigger\n                  value=\"crea-utente\"\n                  className=\"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent\"\n                >\n                  <UserPlus className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">Crea Nuovo Utente</span>\n                </TabsTrigger>\n                {selectedUser && (\n                  <TabsTrigger\n                    value=\"modifica-utente\"\n                    className=\"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent\"\n                  >\n                    <Edit className=\"h-4 w-4\" />\n                    <span className=\"font-medium\">Modifica Utente</span>\n                  </TabsTrigger>\n                )}\n                <TabsTrigger\n                  value=\"database-tipologie-cavi\"\n                  className=\"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent\"\n                >\n                  <Cable className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">Database Tipologie Cavi</span>\n                </TabsTrigger>\n                <TabsTrigger\n                  value=\"visualizza-database-raw\"\n                  className=\"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent\"\n                >\n                  <Database className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">Gestione Dati Avanzata</span>\n                </TabsTrigger>\n              </TabsList>\n            </div>\n\n            {/* Sezione separata per azioni pericolose */}\n            <div className=\"bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-200 shadow-sm p-1\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3 px-4 py-2\">\n                  <div className=\"w-2 h-2 bg-red-500 rounded-full animate-pulse\"></div>\n                  <span className=\"text-sm font-medium text-red-800\">Impostazioni Avanzate e Pericolose</span>\n                </div>\n                <TabsList className=\"h-auto bg-transparent p-0\">\n                  <TabsTrigger\n                    value=\"reset-database\"\n                    className=\"admin-tab-danger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-red-100 data-[state=active]:text-red-700 data-[state=active]:border-red-300 data-[state=active]:shadow-sm hover:bg-red-100 hover:text-red-700 border border-transparent text-red-600 font-medium\"\n                  >\n                    <RotateCcw className=\"h-4 w-4\" />\n                    <span>Reset Database</span>\n                  </TabsTrigger>\n                </TabsList>\n              </div>\n            </div>\n          </div>\n\n          {/* Tab Visualizza Utenti */}\n          <TabsContent value=\"visualizza-utenti\" className=\"space-y-4\">\n\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <p className=\"text-red-600\">{error}</p>\n              </div>\n            )}\n\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <Users className=\"h-5 w-5 text-blue-600\" />\n                      Lista Utenti\n                    </CardTitle>\n                    <CardDescription>\n                      Gestisci tutti gli utenti del sistema CABLYS\n                    </CardDescription>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"relative\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n                      <Input\n                        placeholder=\"Cerca per username, email o ragione sociale...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                        className=\"pl-10 w-80\"\n                      />\n                    </div>\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      {filteredUsers.length} utenti\n                    </Badge>\n                  </div>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"overflow-x-auto\">\n                  <div className=\"rounded-md border\">\n                    <Table>\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead className=\"w-[50px] text-center\">ID</TableHead>\n                        <TableHead className=\"w-[100px]\">Username</TableHead>\n                        <TableHead className=\"w-[80px] text-center\">Password</TableHead>\n                        <TableHead className=\"w-[80px] text-center\">Ruolo</TableHead>\n                        <TableHead className=\"w-[180px]\">Ragione Sociale</TableHead>\n                        <TableHead className=\"w-[160px]\">Email</TableHead>\n                        <TableHead className=\"w-[80px] text-center\">VAT</TableHead>\n                        <TableHead className=\"w-[80px] text-center\">Nazione</TableHead>\n                        <TableHead className=\"w-[120px]\">Referente</TableHead>\n                        <TableHead className=\"w-[90px] text-center\">Scadenza</TableHead>\n                        <TableHead className=\"w-[80px] text-center\">Stato</TableHead>\n                        <TableHead className=\"w-[100px] text-center\">Azioni</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {isLoading ? (\n                        <TableRow>\n                          <TableCell colSpan={12} className=\"text-center py-8\">\n                            <div className=\"flex items-center justify-center gap-2\">\n                              <Loader2 className=\"h-4 w-4 animate-spin\" />\n                              Caricamento...\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      ) : users.length === 0 ? (\n                        <TableRow>\n                          <TableCell colSpan={12} className=\"text-center py-8 text-slate-500\">\n                            Nessun utente trovato\n                          </TableCell>\n                        </TableRow>\n                      ) : (\n                        filteredUsers.map((utente) => (\n                          <TableRow\n                            key={utente.id_utente}\n                            className=\"users-table-row\"\n                          >\n                            {/* ID */}\n                            <TableCell className=\"text-center\">\n                              <Badge variant=\"outline\" className=\"text-xs font-mono\">\n                                #{utente.id_utente}\n                              </Badge>\n                            </TableCell>\n\n                            {/* Username */}\n                            <TableCell className=\"font-semibold text-slate-900\">\n                              {utente.username}\n                            </TableCell>\n\n                            {/* Password - Sempre mascherata per sicurezza */}\n                            <TableCell className=\"text-center\">\n                              <div className=\"flex items-center justify-center gap-1\">\n                                <div className=\"flex gap-1\">\n                                  {[...Array(8)].map((_, i) => (\n                                    <div key={i} className=\"w-1.5 h-1.5 bg-slate-400 rounded-full\"></div>\n                                  ))}\n                                </div>\n                                <div className={`w-2 h-2 rounded-full ml-2 ${\n                                  utente.password_plain ? 'bg-green-500' : 'bg-red-500'\n                                }`} title={utente.password_plain ? 'Password configurata' : 'Password non configurata'}></div>\n                              </div>\n                            </TableCell>\n\n                            {/* Ruolo */}\n                            <TableCell className=\"text-center\">\n                              {getRuoloBadge(utente.ruolo)}\n                            </TableCell>\n\n                            {/* Ragione Sociale */}\n                            <TableCell className=\"max-w-[250px] truncate\" title={utente.ragione_sociale}>\n                              <span className=\"text-slate-900\">{utente.ragione_sociale || '-'}</span>\n                            </TableCell>\n\n                            {/* Email */}\n                            <TableCell className=\"max-w-[200px] truncate text-sm text-slate-600\" title={utente.email}>\n                              {utente.email || '-'}\n                            </TableCell>\n\n                            {/* VAT */}\n                            <TableCell className=\"text-center text-sm text-slate-600\">\n                              {utente.vat || '-'}\n                            </TableCell>\n\n                            {/* Nazione */}\n                            <TableCell className=\"text-center text-sm text-slate-600\">\n                              {utente.nazione || '-'}\n                            </TableCell>\n\n                            {/* Referente */}\n                            <TableCell className=\"max-w-[150px] truncate text-sm text-slate-600\" title={utente.referente_aziendale}>\n                              {utente.referente_aziendale || '-'}\n                            </TableCell>\n\n                            {/* Scadenza */}\n                            <TableCell className=\"text-center text-sm text-slate-600\">\n                              {utente.data_scadenza ?\n                                new Date(utente.data_scadenza).toLocaleDateString('it-IT') :\n                                'N/A'\n                              }\n                            </TableCell>\n\n                            {/* Stato */}\n                            <TableCell className=\"text-center\">\n                              {getStatusBadge(utente.abilitato, utente.data_scadenza)}\n                            </TableCell>\n\n                            {/* Azioni */}\n                            <TableCell className=\"text-center\">\n                              <div className=\"flex items-center justify-center gap-2\">\n                                {/* Icone azioni semplici */}\n                                <SimpleActions\n                                  user={utente}\n                                  onEdit={() => handleEditUser(utente)}\n                                  onToggleStatus={() => handleToggleUserStatus(utente.id_utente)}\n                                  onDelete={() => handleDeleteUser(utente.id_utente)}\n                                />\n\n                                {/* Pulsante Entra */}\n                                <PrimaryButton\n                                  size=\"sm\"\n                                  onClick={() => handleQuickImpersonate(utente)}\n                                  disabled={utente.ruolo === 'owner' || !utente.abilitato}\n                                  className=\"px-3 py-1.5 text-xs\"\n                                  icon={<LogIn className=\"h-3.5 w-3.5\" />}\n                                >\n                                  Entra\n                                </PrimaryButton>\n                              </div>\n                            </TableCell>\n                          </TableRow>\n                        ))\n                      )}\n                    </TableBody>\n                    </Table>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Tab Crea Nuovo Utente */}\n          <TabsContent value=\"crea-utente\" className=\"space-y-4\">\n            <UserForm\n              user={null}\n              onSave={handleSaveUser}\n              onCancel={handleCancelForm}\n            />\n          </TabsContent>\n\n          {/* Tab Modifica Utente - Visibile solo quando un utente è selezionato */}\n          {selectedUser && (\n            <TabsContent value=\"modifica-utente\" className=\"space-y-4\">\n              <UserForm\n                user={selectedUser}\n                onSave={handleSaveUser}\n                onCancel={handleCancelForm}\n              />\n            </TabsContent>\n          )}\n\n          {/* Tab Database Tipologie Cavi */}\n          <TabsContent value=\"database-tipologie-cavi\" className=\"space-y-4\">\n            <TipologieCaviManager />\n          </TabsContent>\n\n          {/* Tab Visualizza Database Raw */}\n          <TabsContent value=\"visualizza-database-raw\" className=\"space-y-4\">\n            <DatabaseView />\n          </TabsContent>\n\n          {/* Tab Reset Database */}\n          <TabsContent value=\"reset-database\" className=\"space-y-4\">\n            <ResetDatabase />\n          </TabsContent>\n\n        </Tabs>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAtBA;;;;;;;;;;;;;;;;;;;;AA6Ce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAO,SAAS;QAAI,UAAU;IAAU;IAEjG,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExC,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,WAAW;QACf,IAAI;YACF,aAAa;YACb,SAAS;YAET,IAAI,cAAc,uBAAuB,cAAc,iBAAiB,cAAc,sBAAsB;gBAC1G,MAAM,YAAY,MAAM,iHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACzC,SAAS;YACX,OAAO,IAAI,cAAc,YAAY;gBACnC,MAAM,eAAe,MAAM,iHAAA,CAAA,cAAW,CAAC,WAAW;gBAClD,YAAY;YACd;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;QAC5D,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAC;YAChC,WAAW,kBAAkB;;QAC/B,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC3C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,iDAAiD;YAC3D,IAAI;gBACF,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;gBAC1B,WAAW,kBAAkB;;YAC/B,EAAE,OAAO,OAAY;gBACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;YAC3C;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,aAAa;QACb,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YAEF,MAAM,gBAAgB,WAAW,SAAS;YAE1C,uDAAuD;YACvD,IAAI,WAAW,KAAK,KAAK,QAAQ;gBAC/B,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,WAAW,KAAK,KAAK,iBAAiB;gBAC/C,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;QAC5D;IACF;IAEA,+BAA+B;IAE/B,MAAM,gBAAgB,CAAC;QACrB,IAAI,YAA8C;QAElD,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF;gBACE,YAAY;gBACZ;QACJ;QAEA,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;QACzC,qBAAO,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAW,aAAa,KAAK;sBAAG;;;;;;IAChD;IAEA,MAAM,iBAAiB,CAAC,WAAoB;QAC1C,IAAI,YAA8C;QAClD,IAAI,QAAQ;QACZ,IAAI,OAAO;QAEX,IAAI,CAAC,WAAW;YACd,YAAY;YACZ,QAAQ;YACR,OAAO;QACT,OAAO,IAAI,eAAe;YACxB,MAAM,WAAW,IAAI,KAAK;YAC1B,MAAM,OAAO,IAAI;YAEjB,IAAI,WAAW,MAAM;gBACnB,YAAY;gBACZ,QAAQ;gBACR,OAAO;YACT,OAAO,IAAI,SAAS,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM;gBACxE,YAAY;gBACZ,QAAQ;gBACR,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF,OAAO;YACL,OAAO;QACT;QAEA,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;QACzC,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAW,GAAG,aAAa,KAAK,CAAC,wBAAwB,CAAC;;8BAC/D,8OAAC;oBAAK,WAAU;oBAAU,MAAK;oBAAM,eAAY;8BAAQ;;;;;;8BACzD,8OAAC;8BAAM;;;;;;;;;;;;IAGb;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,IACjC,EAAE,QAAQ,EAAE,cAAc,SAAS,WAAW,WAAW,OACzD,EAAE,eAAe,EAAE,cAAc,SAAS,WAAW,WAAW,OAChE,EAAE,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW;IAGxD,sDAAsD;IACtD,iFAAiF;IACjF,IAAI,CAAC,MAAM;QACT,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,IAAI,KAAK,KAAK,KAAK,SAAS;QAC1B,uCAAmC;;QAEnC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBAKb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAW,CAAC,YAAY,EAAE,eAAe,gBAAgB,cAAc,gCAAgC,CAAC;;sDAChH,8OAAC,gIAAA,CAAA,cAAW;4CACV,OAAM;4CACN,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,8OAAC,gIAAA,CAAA,cAAW;4CACV,OAAM;4CACN,WAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;wCAE/B,8BACC,8OAAC,gIAAA,CAAA,cAAW;4CACV,OAAM;4CACN,WAAU;;8DAEV,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAGlC,8OAAC,gIAAA,CAAA,cAAW;4CACV,OAAM;4CACN,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,8OAAC,gIAAA,CAAA,cAAW;4CACV,OAAM;4CACN,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;;;;;;;;;;;;0CAMpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,8OAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;sDAClB,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAoB,WAAU;;4BAE9C,uBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;0CAIjC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAA0B;;;;;;;sEAG7C,8OAAC,gIAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oEAC7C,WAAU;;;;;;;;;;;;sEAGd,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAChC,cAAc,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAK9B,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sEACN,8OAAC,iIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kFACP,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAY;;;;;;kFACjC,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAuB;;;;;;kFAC5C,8OAAC,iIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAwB;;;;;;;;;;;;;;;;;sEAGjD,8OAAC,iIAAA,CAAA,YAAS;sEACP,0BACC,8OAAC,iIAAA,CAAA,WAAQ;0EACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oEAAC,SAAS;oEAAI,WAAU;8EAChC,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iNAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAyB;;;;;;;;;;;;;;;;uEAKhD,MAAM,MAAM,KAAK,kBACnB,8OAAC,iIAAA,CAAA,WAAQ;0EACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oEAAC,SAAS;oEAAI,WAAU;8EAAkC;;;;;;;;;;uEAKtE,cAAc,GAAG,CAAC,CAAC,uBACjB,8OAAC,iIAAA,CAAA,WAAQ;oEAEP,WAAU;;sFAGV,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;;oFAAoB;oFACnD,OAAO,SAAS;;;;;;;;;;;;sFAKtB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,QAAQ;;;;;;sFAIlB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGACZ;+FAAI,MAAM;yFAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gGAAY,WAAU;+FAAb;;;;;;;;;;kGAGd,8OAAC;wFAAI,WAAW,CAAC,0BAA0B,EACzC,OAAO,cAAc,GAAG,iBAAiB,cACzC;wFAAE,OAAO,OAAO,cAAc,GAAG,yBAAyB;;;;;;;;;;;;;;;;;sFAKhE,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,cAAc,OAAO,KAAK;;;;;;sFAI7B,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;4EAAyB,OAAO,OAAO,eAAe;sFACzE,cAAA,8OAAC;gFAAK,WAAU;0FAAkB,OAAO,eAAe,IAAI;;;;;;;;;;;sFAI9D,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;4EAAgD,OAAO,OAAO,KAAK;sFACrF,OAAO,KAAK,IAAI;;;;;;sFAInB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,GAAG,IAAI;;;;;;sFAIjB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,OAAO,IAAI;;;;;;sFAIrB,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;4EAAgD,OAAO,OAAO,mBAAmB;sFACnG,OAAO,mBAAmB,IAAI;;;;;;sFAIjC,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,OAAO,aAAa,GACnB,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB,CAAC,WAClD;;;;;;sFAKJ,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,eAAe,OAAO,SAAS,EAAE,OAAO,aAAa;;;;;;sFAIxD,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFACnB,cAAA,8OAAC;gFAAI,WAAU;;kGAEb,8OAAC,6IAAA,CAAA,UAAa;wFACZ,MAAM;wFACN,QAAQ,IAAM,eAAe;wFAC7B,gBAAgB,IAAM,uBAAuB,OAAO,SAAS;wFAC7D,UAAU,IAAM,iBAAiB,OAAO,SAAS;;;;;;kGAInD,8OAAC,8IAAA,CAAA,gBAAa;wFACZ,MAAK;wFACL,SAAS,IAAM,uBAAuB;wFACtC,UAAU,OAAO,KAAK,KAAK,WAAW,CAAC,OAAO,SAAS;wFACvD,WAAU;wFACV,oBAAM,8OAAC,wMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;kGACxB;;;;;;;;;;;;;;;;;;mEA1FA,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA2GvC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAc,WAAU;kCACzC,cAAA,8OAAC,uIAAA,CAAA,UAAQ;4BACP,MAAM;4BACN,QAAQ;4BACR,UAAU;;;;;;;;;;;oBAKb,8BACC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAkB,WAAU;kCAC7C,cAAA,8OAAC,uIAAA,CAAA,UAAQ;4BACP,MAAM;4BACN,QAAQ;4BACR,UAAU;;;;;;;;;;;kCAMhB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAA0B,WAAU;kCACrD,cAAA,8OAAC,mJAAA,CAAA,UAAoB;;;;;;;;;;kCAIvB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAA0B,WAAU;kCACrD,cAAA,8OAAC,2IAAA,CAAA,UAAY;;;;;;;;;;kCAIf,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAiB,WAAU;kCAC5C,cAAA,8OAAC,4IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}]}