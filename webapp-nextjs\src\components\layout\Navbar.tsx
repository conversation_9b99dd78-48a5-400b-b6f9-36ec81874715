'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { PrimaryButton, QuickButton } from '@/components/ui/animated-button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import {
  Cable,
  Home,
  Activity,
  BarChart3,
  Settings,
  Users,
  Menu,
  X,
  Building2,
  ClipboardList,
  FileText,
  LogOut,
  Package,
  ChevronDown,
  Upload,
  Download,
  Shield,
  User,
  Key,
  UserCog,
  Database,
  RotateCcw
} from 'lucide-react'

const getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any, cantiereId?: number) => {
  // Home button - testo personalizzato come nella webapp originale
  const homeButton = {
    name: userRole === 'owner' ? "Menu Admin" :
          userRole === 'user' ? "Lista Cantieri" :
          userRole === 'cantieri_user' ? "Gestione Cavi" : "Home",
    href: userRole === 'owner' ? '/admin' :
          userRole === 'user' ? '/cantieri' :
          userRole === 'cantieri_user' ? '/cavi' : '/',
    icon: Home
  }

  if (userRole === 'owner' && !isImpersonating) {
    // Solo amministratore - solo il pulsante Home che va al pannello admin
    return [homeButton]
  }

  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {
    // Utente standard - Home + eventualmente cantieri se impersonificato
    const nav = [homeButton]
    if (isImpersonating) {
      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })
    }

    // Se un cantiere è selezionato, aggiungi i menu di gestione come nella webapp originale
    if (cantiereId) {
      nav.push(
        { name: 'Visualizza Cavi', href: '/cavi', icon: Cable },
        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },
        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },
        { name: 'Report', href: '/reports', icon: BarChart3 },
        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },
        { name: 'Produttività', href: '/productivity', icon: Activity },
      )
    }

    return nav
  }

  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {
    // Utente cantiere - menu completo come nella webapp originale
    const nav = [homeButton]

    // Se un cantiere è selezionato, aggiungi i menu di gestione
    if (cantiereId) {
      // Se non è cantieri_user diretto, aggiungi Visualizza Cavi
      if (userRole !== 'cantieri_user') {
        nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })
      }

      nav.push(
        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },
        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },
        { name: 'Report', href: '/reports', icon: BarChart3 },
        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },
        { name: 'Produttività', href: '/productivity', icon: Activity },
      )
    }

    return nav
  }

  // Default
  return [homeButton]
}

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [excelDropdownOpen, setExcelDropdownOpen] = useState(false)
  const [adminDropdownOpen, setAdminDropdownOpen] = useState(false)
  const [userDropdownOpen, setUserDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const adminDropdownRef = useRef<HTMLDivElement>(null)
  const userDropdownRef = useRef<HTMLDivElement>(null)
  const pathname = usePathname()
  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()

  // Recupera l'ID del cantiere selezionato dal localStorage o dal context
  const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)
  const cantiereName = cantiere?.commessa || (typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') : '') || `Cantiere ${cantiereId}`

  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser, cantiereId)

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setExcelDropdownOpen(false)
      }
      if (adminDropdownRef.current && !adminDropdownRef.current.contains(event.target as Node)) {
        setAdminDropdownOpen(false)
      }
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Non mostrare navbar nella pagina di login
  if (pathname === '/login') {
    return null
  }

  // Se non autenticato, non mostrare navbar
  if (!isAuthenticated) {
    return null
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm">
      <div className="max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">

          {/* Tutto a sinistra: Logo + Menu Admin + Navigation */}
          <div className="flex items-center space-x-6">
            {/* Logo e Brand */}
            <div className="flex items-center space-x-3 cursor-default">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                <Cable className="w-5 h-5 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-slate-900">CABLYS</h1>
                <p className="text-xs text-slate-500 -mt-1">Cable Installation System</p>
              </div>
            </div>

            {/* Menu Admin Dropdown - solo per owner */}
            {user?.ruolo === 'owner' && !isImpersonating && (
              <div className="relative" ref={adminDropdownRef}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent text-slate-700 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  onClick={() => setAdminDropdownOpen(!adminDropdownOpen)}
                  aria-expanded={adminDropdownOpen}
                  aria-haspopup="true"
                >
                  <Shield className="w-4 h-4" />
                  <span className="hidden lg:inline font-medium">Menu Admin</span>
                  <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${adminDropdownOpen ? 'rotate-180' : ''}`} />
                </Button>

                {adminDropdownOpen && (
                  <div className="absolute top-full left-0 mt-1 w-56 bg-white border border-slate-200 rounded-md shadow-lg z-50">
                    <div className="py-1">
                      <Link
                        href="/admin"
                        className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 transition-colors duration-150"
                        onClick={() => setAdminDropdownOpen(false)}
                      >
                        <div className="flex items-center space-x-3">
                          <Users className="w-4 h-4 text-slate-500" />
                          <span>Pannello Amministrazione</span>
                        </div>
                      </Link>
                      <Link
                        href="/admin?tab=users"
                        className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 transition-colors duration-150"
                        onClick={() => setAdminDropdownOpen(false)}
                      >
                        <div className="flex items-center space-x-3">
                          <Users className="w-4 h-4 text-slate-500" />
                          <span>Gestione Utenti</span>
                        </div>
                      </Link>
                      <Link
                        href="/admin?tab=database"
                        className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 transition-colors duration-150"
                        onClick={() => setAdminDropdownOpen(false)}
                      >
                        <div className="flex items-center space-x-3">
                          <Database className="w-4 h-4 text-slate-500" />
                          <span>Gestione Database</span>
                        </div>
                      </Link>
                      <div className="border-t border-slate-200 my-1"></div>
                      <Link
                        href="/admin?tab=reset"
                        className="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"
                        onClick={() => setAdminDropdownOpen(false)}
                      >
                        <div className="flex items-center space-x-3">
                          <RotateCcw className="w-4 h-4 text-red-500" />
                          <span className="font-medium">Impostazioni Avanzate</span>
                        </div>
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Navigation Desktop - allontanata dal logo */}
            <div className="hidden md:flex items-center space-x-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href ||
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon

              // Gestione speciale per il dropdown Excel
              if (item.hasDropdown && item.name === 'Gestione Excel') {
                return (
                  <div key={item.name} className="relative" ref={dropdownRef}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent ${
                        isActive ? 'bg-blue-100 text-blue-700' : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200'
                      }`}
                      onClick={() => setExcelDropdownOpen(!excelDropdownOpen)}
                    >
                      <Icon className="w-4 h-4" />
                      <span className="hidden lg:inline">{item.name}</span>
                      <ChevronDown className="w-3 h-3" />
                    </Button>

                    {excelDropdownOpen && (
                      <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-slate-200 rounded-md shadow-lg z-50">
                        <div className="py-1">
                          <Link
                            href="/excel/import"
                            className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                            onClick={() => setExcelDropdownOpen(false)}
                          >
                            <div className="flex items-center space-x-2">
                              <Upload className="w-4 h-4" />
                              <span>Importa Excel</span>
                            </div>
                          </Link>
                          <Link
                            href="/excel/export"
                            className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                            onClick={() => setExcelDropdownOpen(false)}
                          >
                            <div className="flex items-center space-x-2">
                              <Download className="w-4 h-4" />
                              <span>Esporta Excel</span>
                            </div>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>
                )
              }

              return (
                <Link key={item.name} href={item.href}>
                  <div className={`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${
                    isActive
                      ? 'text-blue-700 bg-blue-50 border border-blue-200 font-medium'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 border border-transparent'
                  }`}>
                    <Icon className="w-4 h-4" />
                    <span className="hidden lg:inline">{item.name}</span>
                  </div>
                </Link>
              )
            })}
            </div>
          </div>

          {/* User Info a destra con più margine */}
          <div className="flex items-center space-x-4 ml-8">
            {/* Display cantiere selezionato - versione compatta */}
            {cantiereId && cantiereId > 0 && (
              <div className="hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md">
                <Building2 className="w-3 h-3 text-blue-600" />
                <div className="text-xs">
                  <span className="text-blue-900 font-medium">{cantiereName}</span>
                </div>
              </div>
            )}

            {/* User Profile Dropdown */}
            <div className="hidden sm:flex items-center space-x-2">
              <div className="relative" ref={userDropdownRef}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent hover:bg-blue-50 hover:border-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                  aria-expanded={userDropdownOpen}
                  aria-haspopup="true"
                >
                  <div className="text-right">
                    <p className="text-sm font-medium text-slate-900">
                      {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}
                      <span className="text-xs text-slate-500 ml-1">
                        ({user?.ruolo === 'owner' ? 'owner' : user?.ruolo || ''})
                      </span>
                    </p>
                  </div>
                  <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                    <User className="w-3 h-3 text-white" />
                  </div>
                  <ChevronDown className={`w-3 h-3 transition-transform duration-200 ${userDropdownOpen ? 'rotate-180' : ''}`} />
                </Button>

                {userDropdownOpen && (
                  <div className="absolute top-full right-0 mt-1 w-48 bg-white border border-slate-200 rounded-md shadow-lg z-50">
                    <div className="py-1">
                      <div className="px-4 py-2 border-b border-slate-200">
                        <p className="text-xs text-slate-500">Connesso come</p>
                        <p className="text-sm font-medium text-slate-900">
                          {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}
                        </p>
                        <p className="text-xs text-slate-500">
                          {user?.ruolo === 'owner' ? 'Amministratore' :
                           user?.ruolo === 'user' ? 'Utente Standard' :
                           user?.ruolo === 'cantieri_user' ? 'Utente Cantiere' : user?.ruolo}
                        </p>
                      </div>

                      <button
                        className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 transition-colors duration-150"
                        onClick={() => {
                          setUserDropdownOpen(false)
                          // TODO: Implementare gestione profilo
                        }}
                      >
                        <div className="flex items-center space-x-3">
                          <UserCog className="w-4 h-4 text-slate-500" />
                          <span>Impostazioni Profilo</span>
                        </div>
                      </button>

                      <button
                        className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 transition-colors duration-150"
                        onClick={() => {
                          setUserDropdownOpen(false)
                          // TODO: Implementare cambio password
                        }}
                      >
                        <div className="flex items-center space-x-3">
                          <Key className="w-4 h-4 text-slate-500" />
                          <span>Cambia Password</span>
                        </div>
                      </button>

                      <div className="border-t border-slate-200 my-1"></div>

                      <button
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-150"
                        onClick={() => {
                          setUserDropdownOpen(false)
                          logout()
                        }}
                        title={isImpersonating ? 'Torna al menu admin' : 'Logout'}
                      >
                        <div className="flex items-center space-x-3">
                          <LogOut className="w-4 h-4 text-red-500" />
                          <span className="font-medium">
                            {isImpersonating ? 'Torna al Menu Admin' : 'Logout'}
                          </span>
                        </div>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(!isOpen)}
                className="text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md"
              >
                {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden border-t border-slate-200 bg-white">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href || 
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon
              
              return (
                <Link key={item.name} href={item.href}>
                  <div
                    className={`w-full flex items-center justify-start space-x-3 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${
                      isActive
                        ? 'text-blue-700 bg-blue-50 border border-blue-200 font-medium'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 border border-transparent'
                    }`}
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.name}</span>
                  </div>
                </Link>
              )
            })}
          </div>
          
          {/* Mobile User Info e Actions */}
          <div className="border-t border-slate-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                  {user ? <User className="w-3 h-3 text-white" /> : <Building2 className="w-3 h-3 text-white" />}
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-900">
                    {isImpersonating && impersonatedUser ? impersonatedUser.username : (user ? user.username : cantiere?.commessa)}
                    <span className="text-xs text-slate-500 ml-1">
                      ({user?.ruolo === 'owner' ? 'owner' : user?.ruolo || 'Cantiere'})
                    </span>
                  </p>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={logout}
                title={isImpersonating ? 'Torna al menu admin' : 'Logout'}
                className="hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>

            {/* Mobile Admin Menu - solo per owner */}
            {user?.ruolo === 'owner' && !isImpersonating && (
              <div className="mt-3 pt-3 border-t border-slate-200">
                <p className="text-xs font-medium text-slate-500 mb-2">AMMINISTRAZIONE</p>
                <div className="space-y-1">
                  <Link
                    href="/admin"
                    className="block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center space-x-3">
                      <Users className="w-4 h-4 text-slate-500" />
                      <span>Pannello Admin</span>
                    </div>
                  </Link>
                  <Link
                    href="/admin?tab=users"
                    className="block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center space-x-3">
                      <Users className="w-4 h-4 text-slate-500" />
                      <span>Gestione Utenti</span>
                    </div>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  )
}
