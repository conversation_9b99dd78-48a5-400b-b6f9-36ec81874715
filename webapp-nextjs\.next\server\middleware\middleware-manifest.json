{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "03ab32754238a739c3e448069db759f7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c560f2cd2e5b0b7547f79d4b34467de6cde16cff5fc1ee86783a6999996e0b5d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f9c8fa83d4e8a64eaf6c9b2314e35af0f1be4a1b2ed6f97b8ab5258e87b46b6c"}}}, "instrumentation": null, "functions": {}}